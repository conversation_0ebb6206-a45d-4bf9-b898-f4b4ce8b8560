{
  // Change this to match your project
  "exclude": ["**/*.test.ts", "**/*.test.js"],
  "compilerOptions": {
    "moduleResolution": "Bundler",
    "module": "ESNext",
    "target": "ES2015",
    // Useful for error cause
    "lib": ["ES2015", "WebWorker", "DOM", "ES2022.Error"],
    "types": ["node", "jest"],
    // Tells TypeScript to read JS files, as
    // normally they are ignored as source files
    "allowJs": true,
    // Generate d.ts files
    "declaration": true,
    // This compiler run should
    // only output d.ts files
    "emitDeclarationOnly": true,
    // go to js file when using IDE functions like
    // "Go to Definition" in VSCode
    "declarationMap": false,
    "strict": true,
    "allowSyntheticDefaultImports": true
  }
}
