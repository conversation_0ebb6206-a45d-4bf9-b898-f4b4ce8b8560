{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "Node", "lib": ["ES2020", "DOM"], "declaration": true, "declarationMap": true, "outDir": "./es", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "preserveWatchOutput": true, "incremental": true, "tsBuildInfoFile": "./es/.tsbuildinfo"}, "include": ["src/**/*"], "exclude": ["**/*.test.ts", "**/*.test.js", "**/*.spec.ts", "**/*.spec.js", "node_modules", "dist", "es"]}