#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取所有包目录
const packagesDir = path.join(__dirname, '../packages');
const pkgDir = path.join(__dirname, '../pkg');

// 读取所有包
const packages = fs.readdirSync(packagesDir).filter(dir => {
  const packagePath = path.join(packagesDir, dir);
  return fs.statSync(packagePath).isDirectory() && 
         fs.existsSync(path.join(packagePath, 'package.json'));
});

console.log(`Found ${packages.length} packages to build:`);
packages.forEach(pkg => console.log(`  - ${pkg}`));

// 特殊包的处理逻辑
const specialPackages = {
  'fcl-bundle': {
    skip: true,
    reason: 'Build tool package, not for ESM compilation'
  },
  'typedefs': {
    skip: true, 
    reason: 'Type definitions only'
  }
};

// 构建每个包
packages.forEach(packageName => {
  console.log(`\n🔨 Building ${packageName}...`);
  
  // 检查是否是特殊包
  if (specialPackages[packageName]?.skip) {
    console.log(`⏭️  Skipping ${packageName}: ${specialPackages[packageName].reason}`);
    return;
  }
  
  const packagePath = path.join(packagesDir, packageName);
  const outputPath = path.join(pkgDir, packageName);
  
  // 检查是否有 src 目录
  const srcPath = path.join(packagePath, 'src');
  if (!fs.existsSync(srcPath)) {
    console.log(`⏭️  Skipping ${packageName}: No src directory found`);
    return;
  }
  
  try {
    // 清理输出目录
    const esPath = path.join(outputPath, 'es');
    if (fs.existsSync(esPath)) {
      execSync(`rm -rf "${esPath}"`, { stdio: 'inherit' });
    }
    
    // 创建 tsconfig.esm.json 如果不存在
    const tsconfigPath = path.join(packagePath, 'tsconfig.esm.json');
    if (!fs.existsSync(tsconfigPath)) {
      const tsconfigContent = {
        "extends": "../../tsconfig.esm.json",
        "compilerOptions": {
          "outDir": `../../pkg/${packageName}/es`,
          "rootDir": "./src"
        },
        "include": ["src/**/*"]
      };
      fs.writeFileSync(tsconfigPath, JSON.stringify(tsconfigContent, null, 2));
    }
    
    // 运行 TypeScript 编译
    console.log(`  📦 Compiling TypeScript...`);
    execSync(`cd "${packagePath}" && npx tsc -p tsconfig.esm.json`, { 
      stdio: 'inherit',
      cwd: packagePath 
    });
    
    // 添加 .js 扩展名
    console.log(`  🔧 Adding .js extensions...`);
    execSync(`cd "${outputPath}" && npx ts-add-js-extension add --dir=es`, { 
      stdio: 'inherit',
      cwd: outputPath 
    });
    
    console.log(`  ✅ ${packageName} built successfully`);
    
  } catch (error) {
    console.error(`  ❌ Failed to build ${packageName}:`, error.message);
  }
});

console.log('\n🎉 ESM build process completed!');
