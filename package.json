{"workspaces": ["./packages/*"], "scripts": {"build": "lerna run build", "start": "npm run build && lerna run start --parallel", "test": "jest", "release": "npm run build && npm run changeset publish", "changeset": "changeset", "clear": "find . -name node_modules -type d -prune -exec rm -rf '{}' + && find . -name dist -type d -prune -exec rm -rf '{}' +", "prettier:check": "prettier --check .", "prettier": "prettier --write ."}, "name": "fcl-js", "devDependencies": {"@babel/preset-typescript": "^7.25.7", "@changesets/changelog-github": "^0.4.8", "@changesets/cli": "^2.27.9", "@types/jest": "^29.5.13", "@types/node": "^18.19.57", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lerna": "^8.1.8", "prettier": "^3.3.3", "prettier-plugin-classnames": "^0.7.3", "prettier-plugin-tailwindcss": "^0.6.8", "ts-jest": "^29.2.5", "typescript": "^5.6.3"}, "optionalDependencies": {"@nx/nx-darwin-arm64": "^17.3.2", "@nx/nx-darwin-x64": "^17.3.2", "@nx/nx-linux-x64-gnu": "^17.3.2", "@nx/nx-win32-x64-msvc": "^17.3.2"}, "dependencies": {"@noble/hashes": "^1.7.1"}}