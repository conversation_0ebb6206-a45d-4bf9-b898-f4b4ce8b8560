{"name": "@onflow/typedefs", "version": "1.6.0", "description": "Flow JS Type Defs", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "devDependencies": {"@onflow/fcl-bundle": "1.7.0", "@types/node": "^18.19.57", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0", "jest-esm-transformer": "1.0.0", "typescript": "^4.9.5"}, "source": "src/index.ts", "main": "dist/typedefs.js", "module": "dist/typedefs.module.js", "unpkg": "dist/typedefs.umd.js", "types": "types/index.d.ts", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "npm run lint && fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch", "lint": "eslint ."}, "dependencies": {"@babel/runtime": "^7.25.7"}}