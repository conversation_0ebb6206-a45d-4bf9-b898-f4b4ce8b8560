{"name": "@onflow/util-address", "version": "1.2.3", "description": "Flow JS SDK Util -- Address", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "devDependencies": {"@babel/preset-typescript": "^7.25.7", "@onflow/fcl-bundle": "1.7.0", "@onflow/types": "1.4.1", "@types/jest": "^29.5.13", "@types/node": "^18.19.57", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0", "typescript": "^4.9.5"}, "source": "src/index.ts", "main": "dist/util-address.js", "module": "dist/util-address.module.js", "unpkg": "dist/util-address.umd.js", "types": "types/index.d.ts", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "npm run lint && fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch", "lint": "eslint ."}, "dependencies": {"@babel/runtime": "^7.25.7"}}