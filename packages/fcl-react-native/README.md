# FCL: Flow Client Library
### Making secure web applications powered by the Flow Blockchain

Flow Client Library (FCL) enables applications to easily integrate with all FCL-compatible wallets and
other services (e.g. (Coming Soon) profiles, private information, notifications). This offers developers a strong
foundation to compose their apps with existing building blocks. It’s currently supported for browser
applications, and will be extended to other platforms.

With FCL, you can:

- Integrate all compatible wallets without any custom code or code injection
- Authenticate users
- Query the Flow Blockchain
- Send transactions (e.g. initializing resources, sending assets, purchasing, etc.)
- Sign transactions through wallet integration avoiding key management

With FCL, you will eventually be able to:

- Personally sign data via FCL Compatible Wallets
- Request Additional User info like Emails

## Status

- **Last Updated:** Mar 2nd 2021
- **Stable:** Yes
- **Risk of Breaking Change:** Low

The things that exists probably won't be changing much externally, we will be adding new functionality in the near future.

## Install

```bash
npm install --save @onflow/fcl @onflow/types
```

## Getting Started

For a detailed guide explaining how to use `@onflow/fcl` to interact with Flow please see the [Flow App Quick Start](https://developers.flow.com/tutorials/flow-app-quickstart)

Having trouble with something? Reach out to us on [Discord](https://discord.gg/k6cZ7QC), we are more than happy to help.
