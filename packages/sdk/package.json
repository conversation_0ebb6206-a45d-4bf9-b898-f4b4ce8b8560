{"name": "@onflow/sdk", "version": "1.9.0", "description": "Flow SDK", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "jest": {"globals": {"PACKAGE_CURRENT_VERSION": "TESTVERSION"}}, "devDependencies": {"@onflow/fcl-bundle": "1.7.0", "@types/uuid": "^9.0.8", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0", "typescript": "^4.9.5"}, "source": "src/sdk.ts", "main": "dist/sdk.js", "module": "dist/sdk.module.js", "unpkg": "dist/sdk.umd.js", "types": "types/sdk.d.ts", "scripts": {"alpha": "npm publish --tag alpha", "prepublishOnly": "npm test && npm run build", "test": "jest", "build": "npm run lint && fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch", "lint": "eslint ."}, "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/config": "1.5.2", "@onflow/rlp": "1.2.3", "@onflow/transport-http": "1.13.0", "@onflow/typedefs": "1.6.0", "@onflow/types": "1.4.1", "@onflow/util-actor": "1.3.4", "@onflow/util-address": "1.2.3", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3", "@onflow/util-template": "1.2.3", "deepmerge": "^4.3.1", "events": "^3.3.0", "sha3": "^2.1.4", "uuid": "^9.0.1"}}