# @onflow/fcl-wagmi-adapter

## 0.0.5

### Patch Changes

- Updated dependencies [[`0b83658f62a428a70074d33875f264fbd48aff1e`](https://github.com/onflow/fcl-js/commit/0b83658f62a428a70074d33875f264fbd48aff1e), [`b9c4ed3b95c2dc73698e45f353a6ef9a48f23cab`](https://github.com/onflow/fcl-js/commit/b9c4ed3b95c2dc73698e45f353a6ef9a48f23cab)]:
  - @onflow/fcl@1.18.0
  - @onflow/fcl-ethereum-provider@0.0.5

## 0.0.5-alpha.2

### Patch Changes

- Updated dependencies []:
  - @onflow/fcl@1.18.0-alpha.2
  - @onflow/fcl-ethereum-provider@0.0.5-alpha.2

## 0.0.5-alpha.1

### Patch Changes

- Updated dependencies []:
  - @onflow/fcl@1.18.0-alpha.1
  - @onflow/fcl-ethereum-provider@0.0.5-alpha.1

## 0.0.5-alpha.0

### Patch Changes

- Updated dependencies [[`b9c4ed3b95c2dc73698e45f353a6ef9a48f23cab`](https://github.com/onflow/fcl-js/commit/b9c4ed3b95c2dc73698e45f353a6ef9a48f23cab)]:
  - @onflow/fcl@1.18.0-alpha.0
  - @onflow/fcl-ethereum-provider@0.0.5-alpha.0

## 0.0.4

### Patch Changes

- [#2382](https://github.com/onflow/fcl-js/pull/2382) [`0feeae00d1ef089df36b381109802bb0b14bf89b`](https://github.com/onflow/fcl-js/commit/0feeae00d1ef089df36b381109802bb0b14bf89b) Thanks [@jribbink](https://github.com/jribbink)! - Remove unused WalletConnect dependencies

- Updated dependencies [[`0feeae00d1ef089df36b381109802bb0b14bf89b`](https://github.com/onflow/fcl-js/commit/0feeae00d1ef089df36b381109802bb0b14bf89b), [`f86b71357696826a5ad7b8e578de76ecebcd2e29`](https://github.com/onflow/fcl-js/commit/f86b71357696826a5ad7b8e578de76ecebcd2e29)]:
  - @onflow/fcl-ethereum-provider@0.0.4
  - @onflow/fcl@1.17.0

## 0.0.3

### Patch Changes

- Updated dependencies []:
  - @onflow/fcl@1.16.1
  - @onflow/fcl-ethereum-provider@0.0.3

## 0.0.2

### Patch Changes

- Updated dependencies [[`6c4d6c3df669883e34951bc1d26edb95441f124d`](https://github.com/onflow/fcl-js/commit/6c4d6c3df669883e34951bc1d26edb95441f124d)]:
  - @onflow/fcl@1.16.0
  - @onflow/fcl-ethereum-provider@0.0.2

## 0.0.1

Initial release of the FCL Wagmi Adapter. This adapter allows you to use FCL wallets within Wagmi in a shared session with FCL-JS.

Read more about this package in [FLIP-316](https://github.com/onflow/flips/pull/317) and the official [Flow Developer Documentation](https://developers.flow.com/tools/clients/fcl-js/cross-vm/rainbowkit-adapter).
