{"name": "@onflow/fcl-bundle", "version": "1.7.0", "description": "FCL Bundler Tool", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "dependencies": {"@babel/plugin-transform-runtime": "^7.25.7", "@babel/preset-env": "^7.25.8", "@babel/preset-typescript": "^7.25.7", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^6.0.1", "@rollup/plugin-terser": "^0.4.4", "commander": "^12.1.0", "lodash": "^4.17.21", "rollup": "^4.24.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-typescript2": "^0.36.0", "rollup-preserve-directives": "^1.1.3"}, "bin": {"fcl-bundle": "src/cli.js"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.26.0"}}