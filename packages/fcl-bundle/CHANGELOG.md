# @onflow/fcl-bundle

## 1.7.0

### Minor Changes

- [#2076](https://github.com/onflow/fcl-js/pull/2076) [`ade292589a0355891f8d684103849c52305b2a8b`](https://github.com/onflow/fcl-js/commit/ade292589a0355891f8d684103849c52305b2a8b) Thanks [@jribbink](https://github.com/jribbink)! - Allow usage of subpath imports

- [#2347](https://github.com/onflow/fcl-js/pull/2347) [`eb2bbcb97fb0b92008780422fc7199b0af68fc90`](https://github.com/onflow/fcl-js/commit/eb2bbcb97fb0b92008780422fc7199b0af68fc90) Thanks [@jribbink](https://github.com/jribbink)! - Add rollup-preserve-directive plugin

## 1.6.0

### Minor Changes

- [#1970](https://github.com/onflow/fcl-js/pull/1970) [`3b31f1c02b78bf178502627e5ca1de81bdfe8f26`](https://github.com/onflow/fcl-js/commit/3b31f1c02b78bf178502627e5ca1de81bdfe8f26) Thanks [@jribbink](https://github.com/jribbink)! - Add `rollup-plugin-postcss` & `@rollup/plugin-image`

### Patch Changes

- [#1983](https://github.com/onflow/fcl-js/pull/1983) [`18d24c8bad7efa0d8741d74f0cf299f89b3622c7`](https://github.com/onflow/fcl-js/commit/18d24c8bad7efa0d8741d74f0cf299f89b3622c7) Thanks [@jribbink](https://github.com/jribbink)! - Update dependencies

## 1.6.0-alpha.1

### Minor Changes

- [#1970](https://github.com/onflow/fcl-js/pull/1970) [`3b31f1c02b78bf178502627e5ca1de81bdfe8f26`](https://github.com/onflow/fcl-js/commit/3b31f1c02b78bf178502627e5ca1de81bdfe8f26) Thanks [@jribbink](https://github.com/jribbink)! - Add `rollup-plugin-postcss` & `@rollup/plugin-image`

## 1.5.1-alpha.0

### Patch Changes

- [#1983](https://github.com/onflow/fcl-js/pull/1983) [`18d24c8bad7efa0d8741d74f0cf299f89b3622c7`](https://github.com/onflow/fcl-js/commit/18d24c8bad7efa0d8741d74f0cf299f89b3622c7) Thanks [@jribbink](https://github.com/jribbink)! - Update dependencies

## 1.5.0

### Minor Changes

- [#1888](https://github.com/onflow/fcl-js/pull/1888) [`90aa24d2`](https://github.com/onflow/fcl-js/commit/90aa24d237e0003bd62b53dd26fcf29ab743595b) Thanks [@jribbink](https://github.com/jribbink)! - Preserve dynamic import statements in CJS modules

## 1.4.2

### Patch Changes

- [#1848](https://github.com/onflow/fcl-js/pull/1848) [`fdd52c45`](https://github.com/onflow/fcl-js/commit/fdd52c45b3a64210c5f716e13aa4d08d3796370c) Thanks [@jribbink](https://github.com/jribbink)! - Patch babel vulnerability

- [#1821](https://github.com/onflow/fcl-js/pull/1821) [`b9c078ce`](https://github.com/onflow/fcl-js/commit/b9c078ce87869c2b41dff07b861cea09a294c6a1) Thanks [@nialexsan](https://github.com/nialexsan)! - Split packages into `@onflow/fcl`, `@onflow/fcl-core`, and `@onflow/fcl-react-native`.

- [#1814](https://github.com/onflow/fcl-js/pull/1814) [`0d09d838`](https://github.com/onflow/fcl-js/commit/0d09d8386c2fc472833df7152467d477f36dddc4) Thanks [@jribbink](https://github.com/jribbink)! - Fix type declarations not fully being generated

## 1.4.2-alpha.2

### Patch Changes

- [#1848](https://github.com/onflow/fcl-js/pull/1848) [`fdd52c45`](https://github.com/onflow/fcl-js/commit/fdd52c45b3a64210c5f716e13aa4d08d3796370c) Thanks [@jribbink](https://github.com/jribbink)! - Patch babel vulnerability

## 1.4.2-alpha.1

### Patch Changes

- [#1814](https://github.com/onflow/fcl-js/pull/1814) [`0d09d838`](https://github.com/onflow/fcl-js/commit/0d09d8386c2fc472833df7152467d477f36dddc4) Thanks [@jribbink](https://github.com/jribbink)! - Fix type declarations not fully being generated

## 1.4.1

### Patch Changes

- [#1807](https://github.com/onflow/fcl-js/pull/1807) [`9430d723`](https://github.com/onflow/fcl-js/commit/9430d7232c272f4acb55f5bcff7be82cef9704d9) Thanks [@jribbink](https://github.com/jribbink)! - Fix versioning & actor bug

## 1.4.0

### Minor Changes

- [#1801](https://github.com/onflow/fcl-js/pull/1801) [`8881394b`](https://github.com/onflow/fcl-js/commit/8881394bc11fea507e330a4c507ef304fe456c42) Thanks [@nialexsan](https://github.com/nialexsan)! - Switched to rollup-plugin-typescript2

- [#1801](https://github.com/onflow/fcl-js/pull/1801) [`8881394b`](https://github.com/onflow/fcl-js/commit/8881394bc11fea507e330a4c507ef304fe456c42) Thanks [@nialexsan](https://github.com/nialexsan)! - TS build

## 1.3.1

### Patch Changes

- [#1707](https://github.com/onflow/fcl-js/pull/1707) [`f7986d9b`](https://github.com/onflow/fcl-js/commit/f7986d9b4e7bba3abc4acbfb036072336d0651cf) Thanks [@nialexsan](https://github.com/nialexsan)! - Deeplink RPC

- [#1680](https://github.com/onflow/fcl-js/pull/1680) [`6193bd42`](https://github.com/onflow/fcl-js/commit/6193bd420371494a975f19fd001af6af012bd72f) Thanks [@nialexsan](https://github.com/nialexsan)! - Updated dependencies

## 1.3.1-alpha.1

### Patch Changes

- [#1707](https://github.com/onflow/fcl-js/pull/1707) [`f7986d9b`](https://github.com/onflow/fcl-js/commit/f7986d9b4e7bba3abc4acbfb036072336d0651cf) Thanks [@nialexsan](https://github.com/nialexsan)! - Deeplink RPC

## 1.3.1-alpha.0

### Patch Changes

- [#1680](https://github.com/onflow/fcl-js/pull/1680) [`6193bd42`](https://github.com/onflow/fcl-js/commit/6193bd420371494a975f19fd001af6af012bd72f) Thanks [@nialexsan](https://github.com/nialexsan)! - Updated dependencies

## 1.3.0

### Minor Changes

- [#1577](https://github.com/onflow/fcl-js/pull/1577) [`d9a49531`](https://github.com/onflow/fcl-js/commit/d9a495316cd03ed0de99e0f01d1b8850a1f0eec4) Thanks [@chasefleming](https://github.com/chasefleming)! - Add npmignore file for build

## 1.3.0-alpha.0

### Minor Changes

- [#1577](https://github.com/onflow/fcl-js/pull/1577) [`d9a49531`](https://github.com/onflow/fcl-js/commit/d9a495316cd03ed0de99e0f01d1b8850a1f0eec4) Thanks [@chasefleming](https://github.com/chasefleming)! - Add npmignore file for build

## 1.2.0

### Minor Changes

- [#1359](https://github.com/onflow/fcl-js/pull/1359) [`9ea98850`](https://github.com/onflow/fcl-js/commit/9ea988503bbf4760bdee2a8bd8098be1cd95acb6) Thanks [@jribbink](https://github.com/jribbink)! - Automatically compress bundle if entry file ends in .min.js

### Patch Changes

- [#1366](https://github.com/onflow/fcl-js/pull/1366) [`ec6f6a65`](https://github.com/onflow/fcl-js/commit/ec6f6a659eae2157cae3def76fab0eeefccd1c28) Thanks [@jribbink](https://github.com/jribbink)! - Set inlineDynamicImports to true as dynamic imports would cause build to fail

## 1.2.0-alpha.0

### Minor Changes

- [#1359](https://github.com/onflow/fcl-js/pull/1359) [`9ea98850`](https://github.com/onflow/fcl-js/commit/9ea988503bbf4760bdee2a8bd8098be1cd95acb6) Thanks [@jribbink](https://github.com/jribbink)! - Automatically compress bundle if entry file ends in .min.js

### Patch Changes

- [#1366](https://github.com/onflow/fcl-js/pull/1366) [`ec6f6a65`](https://github.com/onflow/fcl-js/commit/ec6f6a659eae2157cae3def76fab0eeefccd1c28) Thanks [@jribbink](https://github.com/jribbink)! - Set inlineDynamicImports to true as dynamic imports would cause build to fail

## 1.1.0

### Minor Changes

- [#1328](https://github.com/onflow/fcl-js/pull/1328) [`cc9cf95e`](https://github.com/onflow/fcl-js/commit/cc9cf95ec40b5a97de8b76bbb00e6c3b35ec7b51) Thanks [@jribbink](https://github.com/jribbink)! - Add banner configuration option (see [README.md](/packages/fcl-bundle/README.md) for usage)

## 1.0.1

### Patch Changes

- [#1227](https://github.com/onflow/fcl-js/pull/1227) [`352f1460`](https://github.com/onflow/fcl-js/commit/352f1460a2f34d228a74fa4bbc6fcf6e68a968b6) Thanks [@jribbink](https://github.com/jribbink)! - @onflow/fcl-bundle tool has been created

## 1.0.1-alpha.0

### Patch Changes

- [#1227](https://github.com/onflow/fcl-js/pull/1227) [`352f1460`](https://github.com/onflow/fcl-js/commit/352f1460a2f34d228a74fa4bbc6fcf6e68a968b6) Thanks [@jribbink](https://github.com/jribbink)! - @onflow/fcl-bundle tool has been created
