{"type": "excalidraw", "version": 1, "source": "https://excalidraw.com", "elements": [{"id": "coRgXgxDx_rGztManLJNj", "type": "line", "x": 1826.82421875, "y": 402.9375, "width": 0, "height": 630.359375, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 1312138966, "version": 662, "versionNonce": 1794938954, "isDeleted": false, "points": [[0, 0], [0, 630.359375]], "lastCommittedPoint": null}, {"id": "M0qjT3k6XLsaUrGM5l_Jk", "type": "text", "x": 1762.546875, "y": 1093.79296875, "width": 128, "height": 35, "angle": 1.5707963267948966, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 590355926, "version": 850, "versionNonce": 1122986326, "isDeleted": false, "text": "Challenge", "font": "28px <PERSON>", "textAlign": "left", "baseline": 25}, {"id": "LLYkOAmehERqY2yfQ4-mS", "type": "line", "x": 1824.30078125, "y": 1033.98828125, "width": 1701.3046875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 1128019606, "version": 124, "versionNonce": 890699530, "isDeleted": false, "points": [[0, 0], [-1701.3046875, 0]], "lastCommittedPoint": null}, {"id": "4jiRvxEhO_mq0VrMwSgL3", "type": "line", "x": 118.14453125, "y": 2993.164306640625, "width": 0, "height": 300.202880859375, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1278829846, "version": 1995, "versionNonce": 1120731158, "isDeleted": false, "points": [[0, 0], [0, 300.202880859375]], "lastCommittedPoint": null}, {"id": "BGN-MZTIw8KwbM7X_KkY2", "type": "line", "x": 118.302734375, "y": 1008.3447265625, "width": 0, "height": 603.4599609375, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 363991382, "version": 867, "versionNonce": 805282966, "isDeleted": false, "points": [[0, 0], [0, 603.4599609375]], "lastCommittedPoint": null}, {"id": "un0BO73k-S9mWt9N8vfWt", "type": "text", "x": 108.84765625, "y": 219.35546875, "width": 45, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1641930582, "version": 151, "versionNonce": 957137290, "isDeleted": false, "text": "dApp", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "chwyHplpBxENYV0oFYUJ5", "type": "text", "x": 506.296875, "y": 218.80859375, "width": 24, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 377033738, "version": 190, "versionNonce": **********, "isDeleted": false, "text": "fcl", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "A_YGfR-4aRXOHoeUej2FH", "type": "text", "x": 871.4375, "y": 219.44921875, "width": 77, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 158, "versionNonce": 567364246, "isDeleted": false, "text": "provider", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "4cHNGYbFyLPBSmllMxnqG", "type": "text", "x": 1277.8984375, "y": 218.390625, "width": 38, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 544904458, "version": 159, "versionNonce": **********, "isDeleted": false, "text": "flow", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "jacsVi7b_9LRNYvWMBgax", "type": "text", "x": 1635.7578125, "y": 215.31640625, "width": 99, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 594396554, "version": 142, "versionNonce": **********, "isDeleted": false, "text": "handshake", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "ryyCHYiHt6qIijgNyuznw", "type": "line", "x": 1686.97265625, "y": 242.30859375, "width": 0, "height": 1078.56640625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 20, "seed": 949453258, "version": 550, "versionNonce": 656589526, "isDeleted": false, "points": [[0, 0], [0, 1078.56640625]], "lastCommittedPoint": null}, {"id": "PdSxGy2bbk0IQ6U3sbaaO", "type": "line", "x": 1295.9375, "y": 250.052734375, "width": 0, "height": 4522.494140625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 20, "seed": 1141283658, "version": 1407, "versionNonce": 1476993046, "isDeleted": false, "points": [[0, 0], [0, 4522.494140625]], "lastCommittedPoint": null}, {"id": "q4v_gQ0kus8ABlQjh2qfU", "type": "line", "x": 907.69921875, "y": 252.095703125, "width": 0, "height": 4519.337890625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 20, "seed": 718607446, "version": 1343, "versionNonce": 561951690, "isDeleted": false, "points": [[0, 0], [0, 4519.337890625]], "lastCommittedPoint": null}, {"id": "sD6hFB9Fg_yS4wv-kXCd7", "type": "line", "x": 518.66015625, "y": 250.041015625, "width": 0, "height": 4521.998046875, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 20, "seed": 1951527242, "version": 1276, "versionNonce": 1296645718, "isDeleted": false, "points": [[0, 0], [0, 4521.998046875]], "lastCommittedPoint": null}, {"id": "qk9t0Pn0gqz1vMQ3-PPVs", "type": "rectangle", "x": 495.0703125, "y": 1090.64453125, "width": 1236.47265625, "height": 518.51953125, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 40, "seed": 1052625546, "version": 134, "versionNonce": 1816547594, "isDeleted": false}, {"id": "WAjcohb9hozxf_x_K2Q70", "type": "line", "x": 127.6796875, "y": 247.509765625, "width": 0, "height": 4524.509765625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 20, "seed": 451624970, "version": 1267, "versionNonce": 1700701898, "isDeleted": false, "points": [[0, 0], [0, 4524.509765625]], "lastCommittedPoint": null}, {"id": "1uT7ljw353mq_pBF1krUo", "type": "arrow", "x": 132.875, "y": 330.609375, "width": 379.125, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 453198294, "version": 247, "versionNonce": 2111004886, "isDeleted": false, "points": [[0, 0], [379.125, 0]], "lastCommittedPoint": null}, {"id": "9_RwWHhR4z7t6UN2sMXZp", "type": "text", "x": -178.890625, "y": 263.02734375, "width": 268, "height": 200, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 1433, "versionNonce": **********, "isDeleted": false, "text": "Scopes allow dApps to request\nadditional (private) information\nfrom the provider. They will be\nsent along to the provider during\nthe initial challenge. If the\n user has decided to allow the\nprovider to share the information,\nsaid information will be available\nin the providers private hooks\nrequest.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 194}, {"id": "3pLEjtL6UX46WlYKdWkJD", "type": "text", "x": 133.08984375, "y": 333.21875, "width": 328, "height": 38, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 251468054, "version": 469, "versionNonce": 592087114, "isDeleted": false, "text": "fcl.config()\n   .put(\"challenge.scope\", \"email\")", "font": "16px Cascadia", "textAlign": "left", "baseline": 34}, {"id": "fbb9O2zUQnZNHHgxrXulZ", "type": "arrow", "x": 134.09765625, "y": 401.00390625, "width": 379.125, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 372, "versionNonce": 526001482, "isDeleted": false, "points": [[0, 0], [379.125, 0]], "lastCommittedPoint": null}, {"id": "V2OPDQAf4l7Ba2i58gZ1n", "type": "text", "x": 131.5078125, "y": 404.61328125, "width": 366, "height": 38, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 392596234, "version": 670, "versionNonce": 1678919446, "isDeleted": false, "text": "fcl.config()\n   .put(\"challenge.scope\", \"publicKey\")", "font": "16px Cascadia", "textAlign": "left", "baseline": 34}, {"id": "KuTZ_hrxsU6Nuq1hi_Pi9", "type": "line", "x": 115.57421875, "y": 278.06640625, "width": 0, "height": 186.05859375, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1103091658, "version": 80, "versionNonce": 777121290, "isDeleted": false, "points": [[0, 0], [0, 186.05859375]], "lastCommittedPoint": null}, {"id": "XJ4RlWOEKco-1mBvuKISA", "type": "line", "x": 67.6875, "y": 355.89453125, "width": 47.6640625, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 269160854, "version": 35, "versionNonce": 1127334922, "isDeleted": false, "points": [[0, 0], [47.6640625, 0]], "lastCommittedPoint": null}, {"id": "BwaqTUADSaiRHoCGokXKI", "type": "text", "x": -178.97265625, "y": 575.17578125, "width": 263, "height": 240, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1391733194, "version": 2490, "versionNonce": 716580118, "isDeleted": false, "text": "Subscribing allows the dApp to\nreactively receive updates about\na given topic. In this case we\nwill want to receive any updates\non the currentUser.\n\nSubscription callbacks are always\ncalled immediately with what we\ncurrently know.\n\nAll data should be considered\nOPTIONAL", "font": "16px <PERSON>", "textAlign": "left", "baseline": 234}, {"id": "gyDGsALUUdgK_TzjT78zF", "type": "arrow", "x": 133.03515625, "y": 575.626953125, "width": 379.125, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 496657494, "version": 370, "versionNonce": 1173122634, "isDeleted": false, "points": [[0, 0], [379.125, 0]], "lastCommittedPoint": null}, {"id": "UBeyLHncVlN6Qy8Z9Yq0P", "type": "text", "x": 134.34765625, "y": 579.634765625, "width": 216, "height": 38, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1793930250, "version": 731, "versionNonce": 66402390, "isDeleted": false, "text": "fcl.currentUser()\n   .subscribe(callback)", "font": "16px Cascadia", "textAlign": "left", "baseline": 34}, {"id": "BhVeXm_7Q-PW9YO_BFdjo", "type": "arrow", "x": 507.73828125, "y": 653.05859375, "width": 374.96484375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 512638486, "version": 230, "versionNonce": 1206320662, "isDeleted": false, "points": [[0, 0], [-374.96484375, 0]], "lastCommittedPoint": null}, {"id": "BA2XTg-utF__4ANomq0DX", "type": "text", "x": 169.609375, "y": 655.84765625, "width": 225, "height": 323, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 12563158, "version": 1969, "versionNonce": **********, "isDeleted": false, "text": "SUBSCRIPTION currentUser\n---\naddr: null\ncid: null\nloggedIn: false\nverified: false\nidentity:\n  name: null\n  avatar: null\nscoped:\n  email: null\n  publicKey: null\nprovider:\n  addr: null\n  pid: null\n  name: null\n  icon: null", "font": "16px Cascadia", "textAlign": "left", "baseline": 319}, {"id": "2STUByBmNYXHz9OqNHNGe", "type": "line", "x": 117.3984375, "y": 559.306640625, "width": 0, "height": 291.021484375, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 429212246, "version": 314, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [0, 291.021484375]], "lastCommittedPoint": null}, {"id": "lOQfNEIEHhNIe2KGKuEQF", "type": "line", "x": 32.31640625, "y": 679.796875, "width": 82.9140625, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 957144778, "version": 453, "versionNonce": 170207498, "isDeleted": false, "points": [[0, 0], [82.9140625, 0]], "lastCommittedPoint": null}, {"id": "AmNNkLgvu36sA-XfvGzvm", "type": "text", "x": 323.33203125, "y": 688.62109375, "width": 493, "height": 300, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 941271766, "version": 1867, "versionNonce": 531821962, "isDeleted": false, "text": "- users flow address\n- a composite id representing the users authentication method\n- has fcl received either public or private hooks?\n- could fcl successfully request private hooks?\n- users profile info\n  - users name per received hooks\n  - users avatar per received hooks\n- private scoped data as requested in the challenge\n  - the optional email from the provider\n  - the optional publicKey from the provider\n- information about the provider\n  - the flow address of provider\n  - the users internal id for the provider\n  - the name of the provider\n  - the providers icon", "font": "16px <PERSON>", "textAlign": "left", "baseline": 294}, {"id": "Qlrsmnvxv0Yu-6XxjPmon", "type": "diamond", "x": 58.4375, "y": 1010.71875, "width": 138.90625, "height": 93.82421875, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 966881814, "version": 948, "versionNonce": 175724182, "isDeleted": false}, {"id": "2Q0NcifLTOiWHZZp7CjVT", "type": "text", "x": 87.32421875, "y": 1032.84375, "width": 84, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 386, "versionNonce": **********, "isDeleted": false, "text": "CLICK\n\"sign in\"", "font": "16px Cascadia", "textAlign": "center", "baseline": 34}, {"id": "KlOxVWTL9pUeM5MOu6mHQ", "type": "arrow", "x": 196.2734375, "y": 1057.07421875, "width": 315.79296875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 250703958, "version": 517, "versionNonce": 101467798, "isDeleted": false, "points": [[0, 0], [315.79296875, 0]], "lastCommittedPoint": null}, {"id": "jVn7DRTBVHuqU6aNGKgn1", "type": "text", "x": 198.6328125, "y": 1062.94140625, "width": 169, "height": 19, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 660400138, "version": 720, "versionNonce": 1689359818, "isDeleted": false, "text": "fcl.authenticate()", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "tDpOm2KyUP_mfcEOtaC6g", "type": "text", "x": -176.26171875, "y": 1083.24609375, "width": 274, "height": 260, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 408947158, "version": 2262, "versionNonce": **********, "isDeleted": false, "text": "The authentication process truly\nstarts here. The user clicks some\nsort of sign in button in the dApp\nwhich triggers the challenge flow.\n\nFCL will then render an iframe\ndisplaying the handshake service.\n\nThe handshake services job is to\nenable a Bring Your Own Provider\ntype of experience, even providers\nfcl and our team have never heard\nof before.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 254}, {"id": "VANgxLAjMWIqDaOMkQGew", "type": "arrow", "x": 526.576171875, "y": 1126.8046875, "width": 1154.728515625, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 778, "versionNonce": 688930710, "isDeleted": false, "points": [[0, 0], [1154.728515625, 0]], "lastCommittedPoint": null}, {"id": "ZwB84bTSRuYNQBJ797UUX", "type": "text", "x": 528.24609375, "y": 1069.0078125, "width": 185, "height": 20, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 186, "versionNonce": **********, "isDeleted": false, "text": "iframe rendered in dApp", "font": "16px <PERSON>", "textAlign": "center", "baseline": 14}, {"id": "3kh7r3_OMznv14O4Q7lds", "type": "text", "x": 528.65625, "y": 1128.5390625, "width": 272, "height": 95, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1752712214, "version": 2306, "versionNonce": 161396042, "isDeleted": false, "text": "HTTP/GET handshake.onflow.org\n---\nl6n: dapp.com\nnonce: NONCE\nscope: email+publicKey", "font": "16px Cascadia", "textAlign": "left", "baseline": 91}, {"id": "I-bSnIv7qk4N11TBrWUvp", "type": "text", "x": 749.22265625, "y": 1167.3671875, "width": 297, "height": 60, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1473737942, "version": 2315, "versionNonce": 140039818, "isDeleted": false, "text": "- the location.origin of the dApp\n- a random value supplied by fcl\n- the special scopes the dApp wants ", "font": "16px <PERSON>", "textAlign": "left", "baseline": 54}, {"id": "xsaHRUOAlZ8ZI4kGqybV5", "type": "diamond", "x": 1616.3515625, "y": 1133.822265625, "width": 138.90625, "height": 93.82421875, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 783279446, "version": 1251, "versionNonce": 525459274, "isDeleted": false}, {"id": "F-S5Qg94dsbKj8AmYRYUm", "type": "text", "x": 1647.96875, "y": 1155.748046875, "width": 75, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 702, "versionNonce": 147359114, "isDeleted": false, "text": "select\nprovider", "font": "16px Cascadia", "textAlign": "center", "baseline": 34}, {"id": "B2msJeeJj2r7pmQ9572Tt", "type": "arrow", "x": 1684.63671875, "y": 1248.70703125, "width": 774.********, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 135045386, "version": 92, "versionNonce": 128871894, "isDeleted": false, "points": [[0, 0], [-774.********, 0]], "lastCommittedPoint": null}, {"id": "QDa016qky6i2rbpghQINV", "type": "text", "x": 944.94140625, "y": 1251.53515625, "width": 206, "height": 95, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 151321174, "version": 2478, "versionNonce": **********, "isDeleted": false, "text": "HTTP/GET provider.com\n---\nl6n: L6N\nnonce: NONCE\nscope: email+publicKey", "font": "16px Cascadia", "textAlign": "left", "baseline": 91}, {"id": "6VaavfynVHFqaOHcJm68y", "type": "text", "x": 1155.625, "y": 1290.04296875, "width": 297, "height": 60, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 2395, "versionNonce": **********, "isDeleted": false, "text": "- the location.origin of the dApp\n- a random value supplied by fcl\n- the special scopes the dApp wants ", "font": "16px <PERSON>", "textAlign": "left", "baseline": 54}, {"id": "NoiopQQrQ6gA3gBnK8QEO", "type": "diamond", "x": 826.49609375, "y": 1331.916015625, "width": 158.109375, "height": 93.82421875, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 212982614, "version": 1492, "versionNonce": 605161866, "isDeleted": false}, {"id": "VSakes544b46aPuOgA12w", "type": "text", "x": 845.65234375, "y": 1350.134765625, "width": 122, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 879537418, "version": 813, "versionNonce": 761283466, "isDeleted": false, "text": "user\nauthenticates", "font": "16px Cascadia", "textAlign": "center", "baseline": 34}, {"id": "TQB93-jsxoUcL9GwaBlZ1", "type": "arrow", "x": 825.63671875, "y": 1377.748046875, "width": 302.73828125, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 92871114, "version": 442, "versionNonce": 629633162, "isDeleted": false, "points": [[0, 0], [-302.73828125, 0]], "lastCommittedPoint": null}, {"id": "SkzWVjyWaeBHWW38XqLMT", "type": "text", "x": 558.888671875, "y": 1392.056640625, "width": 300, "height": 190, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 3074, "versionNonce": **********, "isDeleted": false, "text": "JS/postMessage l6N\n---\ntype: \"FCL::CHALLENGE::RESPONSE\"\naddr: FLOW_ADDRESS\npaddr: PROVIDER_FLOW_ADDRESS\ncode: CODE\nexp: CODE_EXPIRES_AT\nhks: HOOKS_URL\nnonce: NONCE\nl6n: L6N", "font": "16px Cascadia", "textAlign": "left", "baseline": 186}, {"id": "25wzZQ-dBZxMxT8MW1Z-d", "type": "text", "x": 868.22265625, "y": 1427.90234375, "width": 672, "height": 160, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 3377, "versionNonce": **********, "isDeleted": false, "text": "- fcl listens for this event type\n- flow address of the user (used to fetch public hooks)\n- flow address of the provider (used to fetch providers info)\n- a token supplied when requesting the provider for private hooks\n- when the code expires (epoch timestamp in the future)\n- where to request the providers private hooks for the user\n- the nonce provided by fcl at the start of the challenge\n- the location.origin of the dApp (also provided by fcl at the start of the challenge)", "font": "16px <PERSON>", "textAlign": "left", "baseline": 154}, {"id": "NXxTdsH-rCQCSFd5SbNvX", "type": "arrow", "x": 1616.328125, "y": 1159.81640625, "width": 308.453125, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 40, "seed": **********, "version": 98, "versionNonce": 62543126, "isDeleted": false, "points": [[0, 0], [-308.453125, 0]], "lastCommittedPoint": null}, {"id": "lnEjAi8iE7V1aahOopRXY", "type": "arrow", "x": 1307.71484375, "y": 1202.07421875, "width": 316.8359375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 40, "seed": 954039370, "version": 50, "versionNonce": 144070166, "isDeleted": false, "points": [[0, 0], [316.8359375, 0]], "lastCommittedPoint": null}, {"id": "1ZVyii2hq0Wpqq8542uW9", "type": "text", "x": 1318.5078125, "y": 1172.37109375, "width": 284, "height": 20, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 40, "seed": **********, "version": 174, "versionNonce": **********, "isDeleted": false, "text": "fetch available providers from chain", "font": "16px <PERSON>", "textAlign": "center", "baseline": 14}, {"id": "X6HUR1_MrW9gTRgJGheAA", "type": "arrow", "x": 523.095703125, "y": 2420.91796875, "width": 379.943359375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 1136, "versionNonce": 94275030, "isDeleted": false, "points": [[0, 0], [379.943359375, 0]], "lastCommittedPoint": null}, {"id": "7PcbZK4cQREATn7XIHt_A", "type": "text", "x": 525.306640625, "y": 2425.44140625, "width": 169, "height": 57, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 1321, "versionNonce": 232123018, "isDeleted": false, "text": "HTTP/GET HOOKS_URL\n---\ncode: CODE", "font": "16px Cascadia", "textAlign": "left", "baseline": 53}, {"id": "42lCsAd4TL61C2ZBEbmuC", "type": "line", "x": 33.7********, "y": 1173.4833984375, "width": 82.9140625, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1664276234, "version": 754, "versionNonce": 934966614, "isDeleted": false, "points": [[0, 0], [82.9140625, 0]], "lastCommittedPoint": null}, {"id": "3WLQiIIioSPeHv0xMKLko", "type": "text", "x": -173.546875, "y": 1886.24609375, "width": 280, "height": 560, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 964205782, "version": 4104, "versionNonce": **********, "isDeleted": false, "text": "FCL should now have the pieces\nit needs to fetch both the public\nand private hooks.\n\nPublic hooks are stored on chain\nin a resource owned by the flow\naddress. They can also point out\nto resources owned by the provider\nallowing the provider to change\nprovider specific detail as needed\nand example of that is moving the\nauthorization endpoint. SENSITIVE\nDATA SHOULD NEVER BE PUT IN\nA PUBLIC HOOK.\n\nPrivate hooks are provided by\nthe provider. They include provider\nspecific overrides to the public\nhooks as well as the additional\nscopes that were requested in\nthe challenge.\n\nFCL will attempt to fetch both\nprivate and public hooks, deep\nmerge the private into the public\nand then let any currentUser\nsubscribers know there is some\nnew data for them.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 554}, {"id": "oByTwhCzigB059e9baKhZ", "type": "arrow", "x": 524.**********, "y": 1639.83203125, "width": 767.**********, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 1012, "versionNonce": 894784022, "isDeleted": false, "points": [[0, 0], [767.**********, 0]], "lastCommittedPoint": null}, {"id": "bQl2f4HdK2Ezo1MVmCQs-", "type": "text", "x": 525.**********, "y": 1640.6640625, "width": 216, "height": 57, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 263301770, "version": 1277, "versionNonce": 1310257610, "isDeleted": false, "text": "FLOW/SCRIPT FETCH_HOOKS\n---\naddress: FLOW_ADDRESS", "font": "16px Cascadia", "textAlign": "left", "baseline": 53}, {"id": "OfQGmyTtgjme0SWUklR_a", "type": "arrow", "x": 1293.146484375, "y": 1722.********, "width": 774.********, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1210260886, "version": 263, "versionNonce": 210434070, "isDeleted": false, "points": [[0, 0], [-774.********, 0]], "lastCommittedPoint": null}, {"id": "zlS_RGPXHJyxMTeGPbPaA", "type": "text", "x": 556.822265625, "y": 1727.609375, "width": 338, "height": 361, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 358067914, "version": 4272, "versionNonce": 895971274, "isDeleted": false, "text": "FLOW/RESPONSE FETCH_HOOKS\n---\naddr: FLOW_ADDRESS\nkeyId: KEY_ID\nidentity:\n  name: NAME\n  avatar: AVATAR\nauthorizations:\n  - id: PROVIDER_AUTHORIZATION_ID\n    addr: PROVIDER_FLOW_ADDRESS\n    method: \"HTTP/POST\"\n    endpoint: AU<PERSON><PERSON><PERSON>ZATION_ENDPOINT\n    data:\n      id: PID\n  - id: PROVIDER_AUTHORIZATION_ID_2\n    addr: PROVIDER_FLOW_ADDRESS\n    method: \"REMOTE\"\n    data:\n      id: PID", "font": "16px Cascadia", "textAlign": "left", "baseline": 357}, {"id": "RpkmMqz9tdMWF-3_bw77s", "type": "text", "x": 731.95703125, "y": 1765.109375, "width": 226, "height": 100, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 253356234, "version": 5160, "versionNonce": **********, "isDeleted": false, "text": "- flow address for user\n- preferred signing key\n- public identity info\n  - an optional name value\n  - an optional avatar value", "font": "16px <PERSON>", "textAlign": "left", "baseline": 94}, {"id": "2z30__NPjKNxvuE9Pyxmf", "type": "text", "x": 916.23046875, "y": 2017.57421875, "width": 629, "height": 40, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 5822, "versionNonce": **********, "isDeleted": false, "text": "an public authorization hook that points to a remote authorization\nit will use the providers flow address and id that is supplied to do the look up", "font": "16px <PERSON>", "textAlign": "left", "baseline": 34}, {"id": "wwWWue6mYk9yxli73gPmW", "type": "line", "x": 901.21875, "y": 1991.62890625, "width": 0, "height": 98.58984375, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 94038550, "version": 363, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [0, 98.58984375]], "lastCommittedPoint": null}, {"id": "egK8LT0KvLONRfcsgWWyx", "type": "arrow", "x": 525.68017578125, "y": 2181.4375, "width": 767.**********, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 1094, "versionNonce": 152448970, "isDeleted": false, "points": [[0, 0], [767.**********, 0]], "lastCommittedPoint": null}, {"id": "oi3nAccLri9ZoY-toeQbo", "type": "text", "x": 529.15*********, "y": 2186.72265625, "width": 281, "height": 57, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 685568074, "version": 1382, "versionNonce": 86931402, "isDeleted": false, "text": "FLOW/SCRIPT FETCH_PROVIDER\n---\naddress: PROVIDER_FLOW_ADDRESS", "font": "16px Cascadia", "textAlign": "left", "baseline": 53}, {"id": "HM8bKmZ_6p8oTn3sLt9NW", "type": "text", "x": 916.25, "y": 1893.*********, "width": 547, "height": 80, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 6267, "versionNonce": *********, "isDeleted": false, "text": "an public authorization hook that is controlled by users flow account\nit can be overloaded by private hooks if the addr and id match.\nOverloading should be avoided where possible as this authorization\nstrategy could still be used by non-currentUser situations", "font": "16px <PERSON>", "textAlign": "left", "baseline": 74}, {"id": "HPG9r6GWIVU-MLDXRhR0c", "type": "line", "x": 901.********, "y": 1880.*********, "width": 0, "height": 105.*********, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": *********, "version": 554, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [0, 105.*********]], "lastCommittedPoint": null}, {"id": "s6PIfFtwf-7CX3o-PHI9l", "type": "arrow", "x": 1291.*********, "y": 2257.9453125, "width": 774.********, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": *********, "version": 367, "versionNonce": 17668362, "isDeleted": false, "points": [[0, 0], [-774.********, 0]], "lastCommittedPoint": null}, {"id": "d-aTpJ55bJetrQsYzT-8o", "type": "text", "x": 554.728515625, "y": 2260.98828125, "width": 253, "height": 114, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 697513750, "version": 4543, "versionNonce": **********, "isDeleted": false, "text": "FLOW/RESPONSE FETCH_HOOKS\n---\naddr: PROVIDER_FLOW_ADDRESS\nname: PROVIDER_NAME\nicon: PROVIDER_ICON\nauthn: AUTHENTICATION_URL", "font": "16px Cascadia", "textAlign": "left", "baseline": 110}, {"id": "2_VuSBa3bFRM7KYWRfqpK", "type": "line", "x": 508.91796875, "y": 1644.27734375, "width": 154.62109375, "height": 311.0625, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 441658570, "version": 350, "versionNonce": 389400074, "isDeleted": false, "points": [[0, 0], [-154.62109375, 311.0625]], "lastCommittedPoint": [-157.6171875, 295.58984375]}, {"id": "5ME55bvkr9r1r5Zqo7QMP", "type": "line", "x": 350.4609375, "y": 2023.38671875, "width": 162.4765625, "height": 160.96875, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 442543050, "version": 54, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [162.4765625, 160.96875]], "lastCommittedPoint": null}, {"id": "jT1SAtxFHgoSlX-HSjGb3", "type": "line", "x": 349.04296875, "y": 2091.76953125, "width": 165.9921875, "height": 330.19921875, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 677853206, "version": 165, "versionNonce": 1014681046, "isDeleted": false, "points": [[0, 0], [165.9921875, 330.19921875]], "lastCommittedPoint": null}, {"id": "lnpb7wSSUK_sV4qXlPQVt", "type": "text", "x": 270.62109375, "y": 2018.0078125, "width": 126, "height": 20, "angle": 4.71238898038469, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 843163990, "version": 190, "versionNonce": 599478794, "isDeleted": false, "text": "Done in parallel", "font": "16px <PERSON>", "textAlign": "center", "baseline": 14}, {"id": "msGB2E2TZJGP7TkawkmAf", "type": "arrow", "x": 900.84765625, "y": 2509.64453125, "width": 379.458984375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1210719318, "version": 490, "versionNonce": 16187478, "isDeleted": false, "points": [[0, 0], [-379.458984375, 0]], "lastCommittedPoint": null}, {"id": "-VVQYfd22ZmqYqXRLxXEm", "type": "text", "x": 554.970703125, "y": 2514.1875, "width": 272, "height": 304, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 5187, "versionNonce": **********, "isDeleted": false, "text": "HTTP/GET HOOKS_URL RESPONSE\n---\naddr: FLOW_ADDRESS\nkeyId: KEY_ID_2\nidentity:\n  name: NAME\n  avatar: AVATAR\nscoped:\n  email: EMAIL\n  publicKey: null\nprovider:\n  addr: PROVIDER_FLOW_ADDRESS\n  pid: PID\n  name: PROVIDER_NAME\n  icon: PROVIDER_ICON\n  authn: AUTHENTICATION_URL", "font": "16px Cascadia", "textAlign": "left", "baseline": 300}, {"id": "OnbDFeHN4fQhUjlz_WLfY", "type": "text", "x": 731.4140625, "y": 2549.73046875, "width": 680, "height": 160, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 7076, "versionNonce": 292326282, "isDeleted": false, "text": "- Users Flow Address\n- overloading the preferred signing key\n- private identity info (will overload public info)\n  in this case the data is the same\n  if it was omitted fcl will use the public info if its there\n- the requested scoped data\n  - user decided to send the email\n  - but also decided to NOT send the publicKey (reminder that everything is optional)", "font": "16px <PERSON>", "textAlign": "left", "baseline": 154}, {"id": "2N9s2KDB46jGhu9W9zxZ5", "type": "text", "x": 835.90234375, "y": 2722.09375, "width": 414, "height": 40, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 7368, "versionNonce": **********, "isDeleted": false, "text": "- flow address of provider (used in composite id)\n- id controlled by the provider (used in composite id)", "font": "16px <PERSON>", "textAlign": "left", "baseline": 34}, {"id": "SUbm9_A_s1r_jH2PsAK_i", "type": "line", "x": 120.42578125, "y": 1632.94189453125, "width": 0, "height": 1330.13232421875, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 1260, "versionNonce": 719119498, "isDeleted": false, "points": [[0, 0], [0, 1330.13232421875]], "lastCommittedPoint": null}, {"id": "zts7CpbK3b8hN6Lf_L4NX", "type": "line", "x": 33.37109375, "y": 2168.4453125, "width": 82.9140625, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 883, "versionNonce": 504499158, "isDeleted": false, "points": [[0, 0], [82.9140625, 0]], "lastCommittedPoint": null}, {"id": "6-8TIlr57ixKs6Sj3Voqe", "type": "text", "x": 110.08984375, "y": 92.10546875, "width": 1183, "height": 45, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1019216458, "version": 772, "versionNonce": 2018618070, "isDeleted": false, "text": "Flow Client Library - Remote Async Authentication & Authorization", "font": "36px <PERSON>", "textAlign": "left", "baseline": 32}, {"id": "p424mWR_hsD0j4YBnrCVw", "type": "text", "x": 110.375, "y": 137.62109375, "width": 303, "height": 50, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1331610518, "version": 428, "versionNonce": 789812618, "isDeleted": false, "text": "Last Updated: April 17th 2020\nVersion: 3", "font": "20px <PERSON>", "textAlign": "left", "baseline": 43}, {"id": "qF5Aeoa9iYz0RqCATeZEH", "type": "text", "x": 1738.421875, "y": 288.890625, "width": 176, "height": 35, "angle": 1.5707963267948966, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 2000233674, "version": 424, "versionNonce": 186896022, "isDeleted": false, "text": "Setup/Config", "font": "28px <PERSON>", "textAlign": "left", "baseline": 25}, {"id": "SwAuz72dvcZhh4zIV1V_W", "type": "line", "x": 1826.83984375, "y": 1178.0390625, "width": 0, "height": 436.82421875, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 2110378634, "version": 1335, "versionNonce": 289846422, "isDeleted": false, "points": [[0, 0], [0, 436.82421875]], "lastCommittedPoint": null}, {"id": "Pr75G8We8YRy0Yi_gYH0l", "type": "text", "x": 1759.25, "y": 1681.26171875, "width": 132, "height": 35, "angle": 1.5707963267948966, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 1327777994, "version": 1007, "versionNonce": 320669642, "isDeleted": false, "text": "Collection", "font": "28px <PERSON>", "textAlign": "left", "baseline": 25}, {"id": "PVT9D64GjjaP2ucRTUfLs", "type": "line", "x": 1826.046875, "y": 1770.51171875, "width": 0, "height": 1215.11328125, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 1522073814, "version": 2352, "versionNonce": 2129637846, "isDeleted": false, "points": [[0, 0], [0, 1215.11328125]], "lastCommittedPoint": null}, {"id": "jvYGx-4d-NipQ0Bpnu05q", "type": "arrow", "x": 508.783203125, "y": 2558.3984375, "width": 374.96484375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 616907798, "version": 440, "versionNonce": 88690262, "isDeleted": false, "points": [[0, 0], [-374.96484375, 0]], "lastCommittedPoint": null}, {"id": "IZXf_z8RKv6FuMhkvkXNf", "type": "text", "x": 170.095703125, "y": 2563.9375, "width": 272, "height": 323, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 2404, "versionNonce": **********, "isDeleted": false, "text": "SUBSCRIPTION currentUser\n---\naddr: FLOW_ADDRESS\ncid: COMPOSITE_ID\nloggedIn: true\nverified: true\nidentity:\n  name: NAME\n  avatar: AVATAR\nscoped:\n  email: EMAIL\n  publicKey: null\nprovider:\n  addr: PROVIDER_FLOW_ADDRESS\n  pid: PID\n  name: PROVIDER_NAME\n  icon: PROVIDER_ICON", "font": "16px Cascadia", "textAlign": "left", "baseline": 319}, {"id": "waRSRXDeKvd5f6JPg0G6E", "type": "diamond", "x": 56.0546875, "y": 3013.853515625, "width": 138.90625, "height": 93.82421875, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 1054, "versionNonce": **********, "isDeleted": false}, {"id": "paxn_sWovWBSehdKXh9Yq", "type": "text", "x": 75.44140625, "y": 3035.978515625, "width": 103, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 961438922, "version": 526, "versionNonce": 604557962, "isDeleted": false, "text": "TRIGGER\nTRANSACTION", "font": "16px Cascadia", "textAlign": "center", "baseline": 34}, {"id": "hrSSY4HIR0R4f4pjnZxdq", "type": "arrow", "x": 195.189453125, "y": 3058.953125, "width": 315.79296875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 94461718, "version": 622, "versionNonce": 1200903050, "isDeleted": false, "points": [[0, 0], [315.79296875, 0]], "lastCommittedPoint": null}, {"id": "6_zL8bXZ7lEK83iY4swSO", "type": "text", "x": 188.287109375, "y": 3066.16796875, "width": 525, "height": 228, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 653804874, "version": 1965, "versionNonce": 1765016086, "isDeleted": false, "text": "const response = await fcl.send([\n  vtx.TRANSFER_FLOW_TOKENS,\n  sdk.params([\n    sdk.param(AMOUNT, t.UFix64),\n    sdk.param(TO_ADDRESS, t.Address)\n  ]),\n  sdk.proposer(fcl.currentUser().proposerAuthorization),\n  sdk.payer(fcl.currentUser().payerAuthorization),\n  sdk.authorizations([\n    fcl.currentUser().authorization\n  ])\n])", "font": "16px Cascadia", "textAlign": "left", "baseline": 224}, {"id": "huhsom-5KjKGKdIYDMOqy", "type": "text", "x": 718.8828125, "y": 3180.875, "width": 300, "height": 60, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 868467722, "version": 7984, "versionNonce": 141727690, "isDeleted": false, "text": "- who is proposing the transaction\n- who is paying for the transaction\n- who are authorizing the transaction", "font": "16px <PERSON>", "textAlign": "left", "baseline": 54}, {"id": "EZpsNzOMm6AF6ard6rzL9", "type": "text", "x": 455.97265625, "y": 3083.23828125, "width": 297, "height": 40, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 771410058, "version": 8237, "versionNonce": 1499597386, "isDeleted": false, "text": "- a verified/provable transaction\n- params to pass to the transaction", "font": "16px <PERSON>", "textAlign": "left", "baseline": 34}, {"id": "zIfBGvs5LZkn3wLsdHnBD", "type": "arrow", "x": 518.708984375, "y": 3285.18359375, "width": 382.380859375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1573066122, "version": 784, "versionNonce": 1662081558, "isDeleted": false, "points": [[0, 0], [382.380859375, 0]], "lastCommittedPoint": null}, {"id": "QeOvoAeQuTwqV9BaVr0RY", "type": "text", "x": 523.40625, "y": 3287.86328125, "width": 272, "height": 190, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1929637910, "version": 2844, "versionNonce": 469574922, "isDeleted": false, "text": "HOOK authorization\n---\npayload: PAYLOAD\naddr: FLOW_ADDRESS\nkeyId: KEY_ID_2\nrole:\n  - proposer\n  - payer\n  - authorizer\ninteraction: INTERACTION_DATA", "font": "16px Cascadia", "textAlign": "left", "baseline": 186}, {"id": "m-dBP7WUdYjECj9H_N-Lw", "type": "text", "x": 702.2421875, "y": 3323.265625, "width": 215, "height": 80, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 566844874, "version": 8350, "versionNonce": 2015418326, "isDeleted": false, "text": "- what needs to be signed\n- flow address to sign with\n- preferred signing key\n- roles to be signing as", "font": "16px <PERSON>", "textAlign": "left", "baseline": 74}, {"id": "uFmYk6gAWEzV926It2dyS", "type": "text", "x": 803.859375, "y": 3457.97265625, "width": 406, "height": 120, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 963183382, "version": 9206, "versionNonce": **********, "isDeleted": false, "text": "- data that can be used to recreate the payload\n  so it can be verified. It should have everything\n  about the transaction so the provider could also\n  use that data in here to give the user a better\n  experience when they go to approve/decline the\n  transaction", "font": "16px <PERSON>", "textAlign": "left", "baseline": 114}, {"id": "VkMECX5n8ypX3ZzyzSoiY", "type": "arrow", "x": 902.**********, "y": 3588.53515625, "width": 379.458984375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 556, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [-379.458984375, 0]], "lastCommittedPoint": null}, {"id": "fTfdefywWKAJLExOheTvm", "type": "text", "x": 554.**********, "y": 3591.27734375, "width": 413, "height": 380, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 403852758, "version": 6211, "versionNonce": **********, "isDeleted": false, "text": "HOOK authorization RESPONSE\n---\nstatus: PENDING\ncompositeSignature:\n  addr: null\n  keyId: null\n  signature: null\nauthorizationUpdates:\n  method: \"HTTP/GET\"\n  endpoint: AUTHZ_INFO_URL\n  data: { aid: AUTHORIZATION_ID }\nlocal:\n  - method: \"BROWSER/IFRAME\"\n    endpoint: LOCAL_IFRAME_ENDPOINT\n    width: WIDTH\n    height: HEIGHT\n    background: BG_COLOR\n  - method: \"BROWSER/EXTENSION\"\n    eventType: PROVIDER_EXTENSION_EVENT_TYPE\n    data: DATA_TO_SEND_EXTENSION", "font": "16px Cascadia", "textAlign": "left", "baseline": 376}, {"id": "dnULn5eXCu25FbJ5ADvgb", "type": "arrow", "x": 520.**********, "y": 3998.94921875, "width": 382.380859375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 782169098, "version": 936, "versionNonce": 318209162, "isDeleted": false, "points": [[0, 0], [382.380859375, 0]], "lastCommittedPoint": null}, {"id": "aXHKmUTpcXq-KXd22YVHZ", "type": "text", "x": 523.**********, "y": 4006.41015625, "width": 234, "height": 57, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 3106, "versionNonce": 796185162, "isDeleted": false, "text": "HOOK authorizationUpdates\n---\naid: AUTHORIZATION_ID", "font": "16px Cascadia", "textAlign": "left", "baseline": 53}, {"id": "Qt5oM_exd-ilHtenCfsx1", "type": "arrow", "x": 899.611328125, "y": 4085.2265625, "width": 379.458984375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1781753622, "version": 639, "versionNonce": 1286590294, "isDeleted": false, "points": [[0, 0], [-379.458984375, 0]], "lastCommittedPoint": null}, {"id": "HkOFxXHDQ51b1hvi6qcyX", "type": "text", "x": 555.01171875, "y": 4090.125, "width": 319, "height": 209, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 308985162, "version": 6371, "versionNonce": 1920465162, "isDeleted": false, "text": "HOOK authorizationUpdates RESPONSE\n---\nstatus: PENDING\ncompositeSignature:\n  addr: null\n  keyId: null\n  signature: null\nauthorizationUpdates:\n  method: \"HTTP/GET\"\n  endpoint: AUTHZ_INFO_URL\n  data: { aid: AUTHORIZATION_ID }", "font": "16px Cascadia", "textAlign": "left", "baseline": 205}, {"id": "3INOWYSzlw6A2i5ZFLpWE", "type": "arrow", "x": 523.39208984375, "y": 4318.509765625, "width": 382.380859375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1575886678, "version": 1069, "versionNonce": 892796950, "isDeleted": false, "points": [[0, 0], [382.380859375, 0]], "lastCommittedPoint": null}, {"id": "1GaZCC06LIykjWF8msHrO", "type": "text", "x": 526.07763671875, "y": 4325.970703125, "width": 234, "height": 57, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1460054282, "version": 3239, "versionNonce": 499725386, "isDeleted": false, "text": "HOOK authorizationUpdates\n---\naid: AUTHORIZATION_ID", "font": "16px Cascadia", "textAlign": "left", "baseline": 53}, {"id": "rJdS3rSR6xZz3q3K8_obb", "type": "arrow", "x": 902.37353515625, "y": 4404.787109375, "width": 379.458984375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 14402710, "version": 772, "versionNonce": 1707773270, "isDeleted": false, "points": [[0, 0], [-379.458984375, 0]], "lastCommittedPoint": null}, {"id": "VeXvW_CPgoYJy_zq6RVC6", "type": "text", "x": 557.45361328125, "y": 4407.943359375, "width": 319, "height": 133, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1595996106, "version": 6605, "versionNonce": 1537513802, "isDeleted": false, "text": "HOOK authorizationUpdates RESPONSE\n---\nstatus: APPROVED\ncompositeSignature:\n  addr: FLOW_ADDRESS\n  keyId: KEY_ID_2\n  signature: SIGNATURE", "font": "16px Cascadia", "textAlign": "left", "baseline": 129}, {"id": "Sd2FuAqCDzEl9_UVKxCdu", "type": "text", "x": 741.82421875, "y": 3627.671875, "width": 256, "height": 40, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 664875030, "version": 8575, "versionNonce": 1250821334, "isDeleted": false, "text": "- the status of the transaction\n- an empty composite signature ", "font": "16px <PERSON>", "textAlign": "left", "baseline": 34}, {"id": "hM2bEW5KNVxetY6j53I3-", "type": "text", "x": 763.0625, "y": 3723.57421875, "width": 421, "height": 20, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1231038090, "version": 8746, "versionNonce": 244451670, "isDeleted": false, "text": "- where and how to get updates for the transaction", "font": "16px <PERSON>", "textAlign": "left", "baseline": 14}, {"id": "fNHFI8o8oO5yAGFPaxe5s", "type": "text", "x": 860, "y": 3798.71484375, "width": 760, "height": 20, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 610817162, "version": 9131, "versionNonce": 414310218, "isDeleted": false, "text": "- lets FCL know if there are local ways of letting the user authorize from their current device", "font": "16px <PERSON>", "textAlign": "left", "baseline": 14}, {"id": "tfACerMEHudHalqtkDVxw", "type": "text", "x": 916.138671875, "y": 3836.7060546875, "width": 366, "height": 60, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1720766870, "version": 6618, "versionNonce": 596298954, "isDeleted": false, "text": "FCL will render an iframe showing the endpoint\nthe iframe will be rendered with the supplied\nwidth, height and background color", "font": "16px <PERSON>", "textAlign": "left", "baseline": 54}, {"id": "lEAUSNRbeExIAbUFoi457", "type": "line", "x": 898.724609375, "y": 3819.2880859375, "width": 0, "height": 92.2431640625, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 617560778, "version": 810, "versionNonce": 687063446, "isDeleted": false, "points": [[0, 0], [0, 92.2431640625]], "lastCommittedPoint": null}, {"id": "VMWNVeWbavakv7ZbvBDZ4", "type": "text", "x": 986.568359375, "y": 3938.26123046875, "width": 373, "height": 20, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1532948950, "version": 6925, "versionNonce": 1496202634, "isDeleted": false, "text": "FCL will send an event to a browser extension", "font": "16px <PERSON>", "textAlign": "left", "baseline": 14}, {"id": "SNdO8VWRuFZGJFHu1jM1s", "type": "line", "x": 975.658203125, "y": 3912.92529296875, "width": 0, "height": 70.047********, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1134706314, "version": 1016, "versionNonce": 1539103638, "isDeleted": false, "points": [[0, 0], [0, 70.047********]], "lastCommittedPoint": null}, {"id": "U-EEL_3O6Lsxb7mROH8Pr", "type": "line", "x": 548.703125, "y": 3801.44091796875, "width": 0, "height": 168.72705078125, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 237420362, "version": 1079, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [0, 168.72705078125]], "lastCommittedPoint": null}, {"id": "VRe96xT0_1RxtHTMIyi62", "type": "text", "x": 346.95703125, "y": 3819.34375, "width": 195, "height": 140, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 7272, "versionNonce": **********, "isDeleted": false, "text": "These will only happen\nfor the currentUser, but\nthe provider should still\nmake sure that the user\nis authenticated before\nshowing any possibly\nsensitive information", "font": "16px <PERSON>", "textAlign": "left", "baseline": 134}, {"id": "58mykrKHV9EaCFvmLqX9I", "type": "text", "x": 913.681640625, "y": 4180.76904296875, "width": 302, "height": 40, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 291843082, "version": 7021, "versionNonce": 506427606, "isDeleted": false, "text": "The same as the above authorization\nresponse but sans the local info", "font": "16px <PERSON>", "textAlign": "left", "baseline": 34}, {"id": "wJIW4AG-R71Cq1nvCREvC", "type": "line", "x": 901.482421875, "y": 4120.62060546875, "width": 0, "height": 184.09423828125, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 64680342, "version": 1105, "versionNonce": 1179937866, "isDeleted": false, "points": [[0, 0], [0, 184.09423828125]], "lastCommittedPoint": null}, {"id": "1_bAu7XiS5YNkJzGVFz-P", "type": "text", "x": 213, "y": 4136.0703125, "width": 249, "height": 120, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 530454218, "version": 7482, "versionNonce": 1808024342, "isDeleted": false, "text": "FCL will continue to request\nauthorizationUpdates until\nthe response no longer returns\nan authorizationUpdates hook\nor the status is APPROVED or\nREFUSED", "font": "16px <PERSON>", "textAlign": "left", "baseline": 114}, {"id": "19wWD272ASOGmsbjs-59d", "type": "line", "x": 443.7265625, "y": 4170.546875, "width": 65.46484375, "height": 129.7421875, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1153278166, "version": 109, "versionNonce": 2074859210, "isDeleted": false, "points": [[0, 0], [65.46484375, -129.7421875]], "lastCommittedPoint": null}, {"id": "FP82AskFm3Lh1tPvv1lq9", "type": "line", "x": 450.05859375, "y": 4240.8671875, "width": 61.82421875, "height": 113.42578125, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 199143626, "version": 255, "versionNonce": 292145110, "isDeleted": false, "points": [[0, 0], [61.82421875, 113.42578125]], "lastCommittedPoint": null}, {"id": "t_6ydp1uC_v23r855FNXE", "type": "arrow", "x": 525.6611328125, "y": 4585, "width": 761.1201171875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 179347786, "version": 1329, "versionNonce": 526737418, "isDeleted": false, "points": [[0, 0], [761.1201171875, 0]], "lastCommittedPoint": null}, {"id": "us3PQTgK_fN8w5Nk4IhG_", "type": "text", "x": 565.6875, "y": 4590.29296875, "width": 319, "height": 19, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 2127509142, "version": 1722, "versionNonce": 2040934282, "isDeleted": false, "text": "FLOW/TRANSACTION submitTransaction", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "GfjjxVGNRF0p99UbfDZjq", "type": "arrow", "x": 1286.1953125, "y": 4646.7734375, "width": 760.6982421875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1092171862, "version": 975, "versionNonce": 53636950, "isDeleted": false, "points": [[0, 0], [-760.6982421875, 0]], "lastCommittedPoint": null}, {"id": "pW8AlxjpdNg5jWX_4n0dM", "type": "text", "x": 567.89453125, "y": 4653.734375, "width": 300, "height": 19, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 432772490, "version": 1993, "versionNonce": 933974614, "isDeleted": false, "text": "FLOW/RESPONSE submitTransaction ", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "pKKtykoGvufR7ERvt-eTi", "type": "arrow", "x": 512.6123046875, "y": 4647.16796875, "width": 379.458984375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1779519574, "version": 863, "versionNonce": 964619158, "isDeleted": false, "points": [[0, 0], [-379.458984375, 0]], "lastCommittedPoint": null}, {"id": "Ap9Cyn92PMXlJBo3aPhoP", "type": "text", "x": 160.5078125, "y": 4652.4609375, "width": 356, "height": 19, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1261986506, "version": 2126, "versionNonce": 2067928278, "isDeleted": false, "text": "const response = await fcl.send([...])", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "u-UfsHkkQadRF6SCVyVx1", "type": "arrow", "x": 136.68359375, "y": 4710.556640625, "width": 377.4296875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1607577482, "version": 823, "versionNonce": 836980298, "isDeleted": false, "points": [[0, 0], [377.4296875, 0]], "lastCommittedPoint": null}, {"id": "3F1bHxsqx2nTNCPGB-6NI", "type": "text", "x": 140.59375, "y": 4715.869140625, "width": 234, "height": 38, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 51013142, "version": 2270, "versionNonce": 451491158, "isDeleted": false, "text": "fcl.transaction(response)\n   .subscribe(callback)", "font": "16px Cascadia", "textAlign": "left", "baseline": 34}, {"id": "-UaYdX-OEaS5wVMrwN5xR", "type": "text", "x": 1744.84375, "y": 3064.501953125, "width": 162, "height": 35, "angle": 1.5707963267948966, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 338788298, "version": 1205, "versionNonce": 1853718154, "isDeleted": false, "text": "Transaction", "font": "28px <PERSON>", "textAlign": "left", "baseline": 25}, {"id": "LuFDPJCxj7GY2koIvvpQY", "type": "line", "x": 1827.22265625, "y": 3166.751953125, "width": 0, "height": 1603.451171875, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 561953238, "version": 2787, "versionNonce": 1627850518, "isDeleted": false, "points": [[0, 0], [0, 1603.451171875]], "lastCommittedPoint": null}, {"id": "0qPJx0BzT51VBvJZZbAtb", "type": "line", "x": 1828.48046875, "y": 1618.515625, "width": 1701.3046875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 1012736982, "version": 218, "versionNonce": 1545674058, "isDeleted": false, "points": [[0, 0], [-1701.3046875, 0]], "lastCommittedPoint": null}, {"id": "pb8U1YVZRiNyhtgwvxJoO", "type": "line", "x": 1823.1328125, "y": 2984.24609375, "width": 1695.5, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 1893180118, "version": 393, "versionNonce": 1208071254, "isDeleted": false, "points": [[0, 0], [-1695.5, 0]], "lastCommittedPoint": null}, {"id": "9mq_YXv-6dfI7LCcLOoZk", "type": "line", "x": 1825.71875, "y": 4770.21484375, "width": 1695.5, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 4, "roughness": 1, "opacity": 40, "seed": 303631562, "version": 488, "versionNonce": 2036524042, "isDeleted": false, "points": [[0, 0], [-1695.5, 0]], "lastCommittedPoint": null}, {"id": "vKI1TN3W9zEYCqXspw6cd", "type": "line", "x": 116.59765625, "y": 3556.2403564453125, "width": 0, "height": 987.7479248046875, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 561630986, "version": 2591, "versionNonce": 1883029270, "isDeleted": false, "points": [[0, 0], [0, 987.7479248046875]], "lastCommittedPoint": null}, {"id": "x_rOBERCd6TqS6QnIH9qV", "type": "line", "x": 116.16015625, "y": 4690.296875, "width": 0, "height": 76.2421875, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 406239574, "version": 2817, "versionNonce": 691342730, "isDeleted": false, "points": [[0, 0], [0, 76.2421875]], "lastCommittedPoint": null}, {"id": "zOwAE2N733ejewShvmEsl", "type": "line", "x": 115.9921875, "y": 4556.8359375, "width": 0, "height": 119.27734375, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 315952906, "version": 3005, "versionNonce": 1280138506, "isDeleted": false, "points": [[0, 0], [0, 119.27734375]], "lastCommittedPoint": null}, {"id": "aDpvsyDq7LP1_OC4cpBxX", "type": "text", "x": 458.748046875, "y": 2862.462890625, "width": 372, "height": 40, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 385734090, "version": 6220, "versionNonce": 1866634186, "isDeleted": false, "text": "A deep merge of the private currentUser data\ninto the public currentUser data", "font": "16px <PERSON>", "textAlign": "left", "baseline": 34}, {"id": "h7Bs0gToLSbntCq0_kmTk", "type": "line", "x": 452.544921875, "y": 2604.6953125, "width": 0, "height": 292.771484375, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1087030230, "version": 626, "versionNonce": 48037846, "isDeleted": false, "points": [[0, 0], [0, 292.771484375]], "lastCommittedPoint": null}, {"id": "toQyT3WzIMdRQiPm4x9M2", "type": "text", "x": -219.69921875, "y": 2983.08203125, "width": 292, "height": 240, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 331266710, "version": 5459, "versionNonce": 1436890454, "isDeleted": false, "text": "When a transaction is initiated fcl\nwill go through a process called\nasynchronous decomposition. In short\nit will try to make sure it has all\nthe things it needs to have a high\nprobability of submitting a successful\ntransaction. As an example if there\nisn't a currentUser but the\ntransaction needs a currentUser, fcl\nwill attempt to authenticate the\nuser before the transaction needs\nany of the currentUsers information.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 234}, {"id": "x_lnq9l_uj6CvcgnVJgeJ", "type": "line", "x": 18.23828125, "y": 3134.0078125, "width": 98.14453125, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 142857302, "version": 1239, "versionNonce": 821686986, "isDeleted": false, "points": [[0, 0], [98.14453125, 0]], "lastCommittedPoint": null}, {"id": "b_B9QDbiNzXEojm8Ot3A6", "type": "line", "x": 118.421875, "y": 3302.0743408203125, "width": 0, "height": 241.8240966796875, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1749654346, "version": 2215, "versionNonce": 568597834, "isDeleted": false, "points": [[0, 0], [0, 241.8240966796875]], "lastCommittedPoint": null}, {"id": "Co3Eq_HoZ3jX0sykDN2jl", "type": "text", "x": -160.5390625, "y": 3359.703125, "width": 257, "height": 60, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 832108426, "version": 5797, "versionNonce": 1452595658, "isDeleted": false, "text": "FCL will attempt to authorize\nthe transaction with the various\nauthorization hooks.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 54}, {"id": "3tVP74pUyHOCD2o9JmG9o", "type": "line", "x": 18.845703125, "y": 3408.4453125, "width": 98.14453125, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 272878102, "version": 1318, "versionNonce": 1339264278, "isDeleted": false, "points": [[0, 0], [98.14453125, 0]], "lastCommittedPoint": null}, {"id": "xlOhPrPEy7PRuivNrLRnv", "type": "text", "x": -177.6328125, "y": 3559.34375, "width": 287, "height": 520, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 190797386, "version": 7155, "versionNonce": **********, "isDeleted": false, "text": "FCL will timeout authorizations\nfairly quickly if they don't respond\nright away. This is because the\nauthorization doesn't actually need\nto happen with this request.\nInstead what this request is doing\nis supplying the provider with what\nto sign, and then the provider sends\nback the information required to\ncreate a backchannel in which FCL\ncan then request the results.\n\nThe provider can also supply some\nlocal strategies that can give the\nuser an in dApp authorization flow.\nThese strategies can be things like\nrendering an iframe or sending an\nevent to a browser extension.\n\nBecause the actual response that\nmatters for the authorization\nhappens in a back channel we can\nlock these local strategies down\nas they really only act as a window\ninto the providers authorization\nuser experience.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 514}, {"id": "NEX_J-rQLNq2wm5mbCd7c", "type": "line", "x": 68.46875, "y": 3788.57421875, "width": 47.951171875, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 317150154, "version": 1438, "versionNonce": 987723158, "isDeleted": false, "points": [[0, 0], [47.951171875, 0]], "lastCommittedPoint": null}, {"id": "JVb2QMCeDhYIHIb3lBAJ8", "type": "line", "x": 1700.62890625, "y": 3370.12890625, "width": 0, "height": 758.33203125, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 20, "seed": 287421898, "version": 3322, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [0, 758.33203125]], "lastCommittedPoint": null}, {"id": "T0FONKIugMmjyhBNPa1b2", "type": "arrow", "x": 908.5869140625, "y": 3392.4375, "width": 783.2568359375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 62204694, "version": 1042, "versionNonce": 1425626198, "isDeleted": false, "points": [[0, 0], [783.2568359375, 0]], "lastCommittedPoint": null}, {"id": "RzUmFjJWM7eyvTR6uIWjl", "type": "arrow", "x": 1694.3984375, "y": 4106, "width": 782.88671875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 302396822, "version": 1558, "versionNonce": 568895754, "isDeleted": false, "points": [[0, 0], [-782.88671875, 0]], "lastCommittedPoint": null}, {"id": "IvLj82FximT02G1AK9jZ_", "type": "diamond", "x": 1622.4375, "y": 3971.888671875, "width": 150.1953125, "height": 93.82421875, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1415433814, "version": 1295, "versionNonce": 1640663126, "isDeleted": false}, {"id": "oXRPPSMK37VNWnLwkYuZP", "type": "text", "x": 1647.4921875, "y": 3995.099609375, "width": 103, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 65890826, "version": 735, "versionNonce": **********, "isDeleted": false, "text": "APPROVED\nTRANSACTION", "font": "16px Cascadia", "textAlign": "center", "baseline": 34}, {"id": "pmu2RB5R3JQnXZ20pKfb6", "type": "text", "x": 1657.11328125, "y": 3281.0546875, "width": 83, "height": 75, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 654, "versionNonce": 399838934, "isDeleted": false, "text": "provider\napproval\nprocess", "font": "20px <PERSON>", "textAlign": "center", "baseline": 68}, {"id": "jNThL9TEd_WZFS1q9bspt", "type": "text", "x": 1474.6015625, "y": 3224.20703125, "width": 206, "height": 160, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 8513, "versionNonce": **********, "isDeleted": false, "text": "These could be things like\n- the providers mobile app\n- a hardware device\n- browser extension\n- iframe\n- text message\n- providers website\n- desktop application", "font": "16px <PERSON>", "textAlign": "left", "baseline": 154}, {"id": "IF08tbolyi3Jhmflhqsew", "type": "line", "x": 1645.6796875, "y": 3292.703125, "width": 0, "height": 63.39501953125, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 1030, "versionNonce": 891123786, "isDeleted": false, "points": [[0, 0], [0, 63.39501953125]], "lastCommittedPoint": null}, {"id": "ljhLjuHekKgxFYwog8Fwn", "type": "line", "x": 1645.95703125, "y": 3325.228759765625, "width": 29.6171875, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ced4da", "fillStyle": "cross-hatch", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 138692298, "version": 1258, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [-29.6171875, 0]], "lastCommittedPoint": null}, {"id": "7wScteC3L1Oah6wsNyl8R", "type": "text", "x": -179.7890625, "y": 4584.96875, "width": 285, "height": 80, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1886238922, "version": 7618, "versionNonce": 286539338, "isDeleted": false, "text": "Once FCL thinks it has everything\nit needs, it will attempt to submit\nthe transaction to the access node\nreturning a Response", "font": "16px <PERSON>", "textAlign": "left", "baseline": 74}, {"id": "Lt5zP-8-JtS3R8KXjCn_y", "type": "line", "x": 97.60546875, "y": 4618.953125, "width": 17.7060546875, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1566435478, "version": 1705, "versionNonce": 30330582, "isDeleted": false, "points": [[0, 0], [17.7060546875, 0]], "lastCommittedPoint": null}, {"id": "_hXtzS2Onepme0YJyWzLw", "type": "text", "x": -149.98828125, "y": 4704.33984375, "width": 248, "height": 60, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 451638870, "version": 7890, "versionNonce": 1114224214, "isDeleted": false, "text": "The response can be used to\nsubscribe to the status of the\ntransaction.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 54}, {"id": "XaIR2rfIdf4M2OpALv-I2", "type": "line", "x": 28.1796875, "y": 4755.40234375, "width": 85.70849609375, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 491140886, "version": 1932, "versionNonce": 1203742282, "isDeleted": false, "points": [[0, 0], [85.70849609375, 0]], "lastCommittedPoint": null}], "appState": {"viewBackgroundColor": "#ffffff"}}