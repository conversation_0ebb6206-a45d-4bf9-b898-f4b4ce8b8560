{"type": "excalidraw", "version": 1, "source": "https://excalidraw.com", "elements": [{"id": "X82DstJUEARnSITNXvNB6", "type": "text", "x": 233.3046875, "y": -169.7421875, "width": 45, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 136086990, "version": 231, "versionNonce": 1135402510, "isDeleted": false, "text": "dApp", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "xH1RqhYL11nkjC3X_8IG6", "type": "text", "x": 706.36328125, "y": -167.02734375, "width": 24, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": **********, "version": 242, "versionNonce": **********, "isDeleted": false, "text": "fcl", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "GEnoVsC2cgiSNqlsaoL9h", "type": "text", "x": 1011.1484375, "y": -169.0859375, "width": 77, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": **********, "version": 489, "versionNonce": 469664274, "isDeleted": false, "text": "provider", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "5BoO927donURMIR02xknP", "type": "text", "x": 1344.03515625, "y": -170.87890625, "width": 99, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 695961166, "version": 424, "versionNonce": 575018382, "isDeleted": false, "text": "handshake", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "BTAcvlT27MyZLn5RUNnfk", "type": "line", "x": 251.87529592803025, "y": -138.464902935606, "width": 0, "height": 3537.261777935606, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": **********, "version": 1714, "versionNonce": 299491794, "isDeleted": false, "points": [[0, 0], [0, 3537.261777935606]], "lastCommittedPoint": null}, {"id": "2CwkGH75FWSk_nFRQq78l", "type": "line", "x": 722.207741477273, "y": -137.5621448863635, "width": 0, "height": 3503.9762073863635, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 2003048206, "version": 1850, "versionNonce": 1851062674, "isDeleted": false, "points": [[0, 0], [0, 3503.9762073863635]], "lastCommittedPoint": null}, {"id": "pq9iTWIOg7ZqsjTjUyydo", "type": "line", "x": 1399.298295454546, "y": -146.30208333333326, "width": 0, "height": 961.9505208333333, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 1504300430, "version": 1734, "versionNonce": 934456334, "isDeleted": false, "points": [[0, 0], [0, 961.9505208333333]], "lastCommittedPoint": null}, {"id": "0vDqMZTf29GvLIjkcMN24", "type": "line", "x": 1050.840317234849, "y": -134.86162405303025, "width": 0, "height": 1829.3303740530303, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 1891407570, "version": 1872, "versionNonce": 2032272462, "isDeleted": false, "points": [[0, 0], [0, 1829.3303740530303]], "lastCommittedPoint": null}, {"id": "9lmBPDK9w0oEEGWepDzny", "type": "arrow", "x": 257.1732954545463, "y": 371.17294034090924, "width": 453.7734375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 1244921938, "version": 750, "versionNonce": 729298062, "isDeleted": false, "points": [[0, 0], [453.7734375, 0]], "lastCommittedPoint": null}, {"id": "-FvsrMYPf0xc-m5CxcwiW", "type": "rectangle", "x": 749.8466796875, "y": 367.115234375, "width": 760.62890625, "height": 518.2880859375, "angle": 0, "strokeColor": "#495057", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1534813390, "version": 883, "versionNonce": 589341394, "isDeleted": false}, {"id": "hO7j8hOrCRU2U3wwsSLzj", "type": "text", "x": 371.7230113636364, "y": 381.8902698863636, "width": 169, "height": 19, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 1445008718, "version": 406, "versionNonce": 509369810, "isDeleted": false, "text": "fcl.authenticate()", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "ikPzB7NmlgsCiM7h48wzz", "type": "text", "x": 763.755681818182, "y": 339.43146306818176, "width": 48, "height": 20, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 1341841742, "version": 210, "versionNonce": 123861010, "isDeleted": false, "text": "iframe", "font": "16px <PERSON>", "textAlign": "left", "baseline": 14}, {"id": "6My7tLh7S7YLT57zWx_vE", "type": "arrow", "x": 727.8359375000005, "y": 390.31356534090924, "width": 21.8359375, "height": 0.484375, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 1351137682, "version": 1020, "versionNonce": 1616856910, "isDeleted": false, "points": [[0, 0], [21.8359375, 0.484375]], "lastCommittedPoint": null}, {"id": "yvj6VuyeHRCPi_vyel0r5", "type": "arrow", "x": 759.4580078125, "y": 394.65447443181824, "width": 618.2982954545455, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 1823087634, "version": 419, "versionNonce": 1799559378, "isDeleted": false, "points": [[0, 0], [618.2982954545455, 0]], "lastCommittedPoint": null}, {"id": "hR3478POWq3jwTfrLLoWQ", "type": "text", "x": 1066.388494318183, "y": 407.9277343750001, "width": 300, "height": 76, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 1005826318, "version": 709, "versionNonce": 208691790, "isDeleted": false, "text": "HTTP/GET handshake.com/authorize\n      ?l6n=dapp.com\n      &nonce=NONCE\n      &scope=email", "font": "16px Cascadia", "textAlign": "left", "baseline": 72}, {"id": "ElxdXQ7lbC9ExIaIWTu0f", "type": "diamond", "x": 1319.576171875, "y": 412.4609375, "width": 159.53125, "height": 109.62890625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 990579794, "version": 590, "versionNonce": **********, "isDeleted": false}, {"id": "UEi0jEievAhBXhf9fLfIU", "type": "text", "x": 1365.3583984374995, "y": 434.14897017045473, "width": 75, "height": 57, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": **********, "version": 604, "versionNonce": **********, "isDeleted": false, "text": "user\nselects\nprovider", "font": "16px Cascadia", "textAlign": "center", "baseline": 53}, {"id": "bNLRMaNe0HSIX5yffdAPN", "type": "arrow", "x": 1383.5680930397734, "y": 525.4327947443182, "width": 322.00390625, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 782498958, "version": 495, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [-322.00390625, 0]], "lastCommittedPoint": null}, {"id": "kLg5eP6E82X_bbmIMxEVT", "type": "text", "x": 1099.349875710228, "y": 528.697265625, "width": 356, "height": 95, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 132491342, "version": 1386, "versionNonce": **********, "isDeleted": false, "text": "HTTP/GET provider.com/authorize\n      ?l6n=dapp.com\n      &nonce=NONCE\n      &redirect=handshake.com/callback\n      &scope=email", "font": "16px Cascadia", "textAlign": "left", "baseline": 91}, {"id": "4e1gnzS6KGy9FpVvwsE0y", "type": "diamond", "x": 966.4159268465914, "y": 543.5696022727273, "width": 170.40625, "height": 109.87890625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": **********, "version": 431, "versionNonce": 459520654, "isDeleted": false}, {"id": "J-7KvrPQ_dcUDyCit0wrN", "type": "text", "x": 989.5032848011365, "y": 572.0363103693185, "width": 122, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 256530706, "version": 293, "versionNonce": 162889042, "isDeleted": false, "text": "user\nauthenticates", "font": "16px Cascadia", "textAlign": "center", "baseline": 34}, {"id": "f__AKQ5JuFy95O3dHP60q", "type": "arrow", "x": 1053.525390625, "y": 675.4022549715908, "width": 299.701349431818, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 452536210, "version": 331, "versionNonce": 2119654418, "isDeleted": false, "points": [[0, 0], [-299.701349431818, 0]], "lastCommittedPoint": null}, {"id": "z3HYPBdyJe3D9Z5YmvVa7", "type": "text", "x": 790.5197088068185, "y": 679.21875, "width": 253, "height": 171, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 324942094, "version": 1118, "versionNonce": **********, "isDeleted": false, "text": "JS/postMessage dapp.com\n{ type: \"FLOW::AUTHN::RESP\"\n  acct: FLOW_ACCT_NUMBER\n pacct: PROVIDER_FLOW_ACCT\n  code: CODE\n   exp: WHEN_CODE_EXPIRES\n   hks: HOOKS_URL\n nonce: NONCE\n   l6n: dapp.com", "font": "16px Cascadia", "textAlign": "left", "baseline": 167}, {"id": "J_YK6v96uJRCM-Mfcd4fX", "type": "arrow", "x": 747.220525568182, "y": 751.5370205965908, "width": 22.13671875, "height": 0.50390625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": **********, "version": 466, "versionNonce": 341474066, "isDeleted": false, "points": [[0, 0], [-22.13671875, 0.50390625]], "lastCommittedPoint": null}, {"id": "z1nKyn4-r_NwUhKly50Dn", "type": "arrow", "x": 257.8700284090918, "y": -29.200639204545496, "width": 454.40234375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 158142606, "version": 413, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [454.40234375, 0]], "lastCommittedPoint": null}, {"id": "vbN6hJoyYF5gPVEd-8rbE", "type": "text", "x": 315.75958806818187, "y": -20.711292613636346, "width": 347, "height": 19, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 1494716046, "version": 389, "versionNonce": 745067346, "isDeleted": false, "text": "fcl.currentUser().subscribe(callback)", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "ZdYenok5PgTqBOZGqWJ9T", "type": "arrow", "x": 710.2102272727283, "y": 87.11186079545473, "width": 495.80078125, "height": 0, "angle": 0, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 1636347534, "version": 628, "versionNonce": 1593011346, "isDeleted": false, "points": [[0, 0], [-495.80078125, 0]], "lastCommittedPoint": null}, {"id": "uBoSR15InMuxDFczmXPNl", "type": "text", "x": 375.4508167613635, "y": 92.19202769886363, "width": 188, "height": 171, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 599148370, "version": 961, "versionNonce": **********, "isDeleted": false, "text": "CALLBACK currentUser\n{ acct: null\n  verified: false\n  name: null\n  avatar: null\n  hooks: null\n  provider: null\n  \n  ", "font": "16px Cascadia", "textAlign": "left", "baseline": 167}, {"id": "kuvYgoF80yizx2t7l1_Wu", "type": "arrow", "x": 253.6885653409098, "y": -108.1796875, "width": 458.5, "height": 0.1015625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 92803918, "version": 196, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [458.5, -0.1015625]], "lastCommittedPoint": null}, {"id": "M05GYmSmMRsGs8ba8L4NE", "type": "text", "x": 295.52734375, "y": -99.0752840909091, "width": 375, "height": 19, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": **********, "version": 360, "versionNonce": **********, "isDeleted": false, "text": "fcl.config().set(\"authn.scope\", \"email\")", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "H2GMhSuYq7R82OOAh6mQU", "type": "line", "x": 209.55959990530283, "y": 53.17418323863649, "width": 0, "height": 1595.1148792613635, "angle": 0, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 1272655506, "version": 1102, "versionNonce": 985401938, "isDeleted": false, "points": [[0, 0], [0, 1595.1148792613635]], "lastCommittedPoint": null}, {"id": "bl1HxYodwSGdHcwgLAmHu", "type": "text", "x": 100.953125, "y": 140.890625, "width": 188, "height": 19, "angle": 4.**************, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 341119374, "version": 231, "versionNonce": 1721629646, "isDeleted": false, "text": "currentUser callback", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "tpx39Uabm9WmwItnYcdL5", "type": "arrow", "x": 728.7411221590909, "y": 920.90625, "width": 319.4375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 100270482, "version": 115, "versionNonce": 1131109842, "isDeleted": false, "points": [[0, 0], [319.4375, 0]], "lastCommittedPoint": null}, {"id": "s3ZENkjpa3X1xnEta75Vc", "type": "text", "x": 773.6104403409096, "y": 922.1803977272724, "width": 169, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 1619662994, "version": 184, "versionNonce": 1482080334, "isDeleted": false, "text": "HTTP/GET HOOKS_URL\n      ?code=CODE", "font": "16px Cascadia", "textAlign": "left", "baseline": 34}, {"id": "GO8hfYa3F6Nt2H5xKQ2hv", "type": "text", "x": 1679.064453125, "y": -178.3564453125, "width": 38, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 2033488786, "version": 518, "versionNonce": 857480850, "isDeleted": false, "text": "flow", "font": "20px <PERSON>", "textAlign": "left", "baseline": 18}, {"id": "YeYjJP5A3sGBrZExuWzFZ", "type": "line", "x": 1700.257930871212, "y": -155.798828125, "width": 0, "height": 3436.556640625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 172459026, "version": 1738, "versionNonce": 2133090578, "isDeleted": false, "points": [[0, 0], [0, 3436.556640625]], "lastCommittedPoint": null}, {"id": "pPk2e2mIzDd-J2s2YNg_L", "type": "arrow", "x": 730.3025568181819, "y": 893.6747159090908, "width": 958.05078125, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 1066210830, "version": 146, "versionNonce": 31432782, "isDeleted": false, "points": [[0, 0], [958.05078125, 0]], "lastCommittedPoint": null}, {"id": "lodWMrmO71NS9ONwZCs22", "type": "text", "x": 1291.889825994319, "y": 897.4580078125, "width": 291, "height": 19, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 2114953742, "version": 302, "versionNonce": 953841870, "isDeleted": false, "text": "FLOW/SCRIPT GET_FLOW_ACCT_HOOKS", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "MrmoALjMi8PE8-Y0jNJqr", "type": "arrow", "x": 1044.0951704545455, "y": 979.98046875, "width": 310.8046875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 2128973778, "version": 91, "versionNonce": 1168364434, "isDeleted": false, "points": [[0, 0], [-310.8046875, 0]], "lastCommittedPoint": null}, {"id": "0idZBeC9LrWuHVBCFoGkf", "type": "text", "x": 737.************, "y": 993.0625000000036, "width": 441, "height": 171, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": **********, "version": 1125, "versionNonce": **********, "isDeleted": false, "text": "HTTP/GET HOOKS_URL RESPONSE\n  { identity: <PERSON>EN<PERSON><PERSON> {\n      email: EMAIL (because requested in scope)\n    provider: {\n      providerId: PROVIDER_FLOW_ACCT\n      uid: UID (provider specific id for acct)\n      hooks: HOOKS_URL\n    hooks: { /* private hooks */ }\n    ", "font": "16px Cascadia", "textAlign": "left", "baseline": 167}, {"id": "ASD61pbFPqloPUWkna7UU", "type": "arrow", "x": 1687.390625, "y": 1198.1015625, "width": 957.0078125, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 263596690, "version": 122, "versionNonce": 904029838, "isDeleted": false, "points": [[0, 0], [-957.0078125, 0]], "lastCommittedPoint": null}, {"id": "zSn4Y-uN2zPsPLBeaHy8z", "type": "text", "x": 1086.66512784091, "y": 1216.2997159090914, "width": 431, "height": 304, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": **********, "version": 1091, "versionNonce": **********, "isDeleted": false, "text": "FLOW/SCRIPT GET_FLOW_ACCT_HOOKS RESPONSE\n  { identity: IDENTITY {\n      acct: FLOW_ACCT_NUMBER\n      name: NAME\n      avatar: AVATAR\n      bio: BIO\n    providers: [{\n      providerId: PROVIDER_ID\n      uid: UID (provider specific id for acct)\n      hooks: HOOKS_URL\n    hooks:\n      authorizations: [{\n        method: \"POST\"\n        endpoint: \"provider.com/authorization\"\n        data: {id: UID}\n    ", "font": "16px Cascadia", "textAlign": "left", "baseline": 300}, {"id": "um7BoVKD9JypyLYQYzSFM", "type": "line", "x": 1193.5969460227266, "y": 992.6349431818178, "width": 0, "height": 185.96875, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 162, "versionNonce": 532411538, "isDeleted": false, "points": [[0, 0], [0, 185.96875]], "lastCommittedPoint": null}, {"id": "Z8pExVkRYjlrhnS37T4QL", "type": "line", "x": 1202.0539772727266, "y": 993.8781960227268, "width": 0, "height": 185.96875, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 199, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [0, 185.96875]], "lastCommittedPoint": null}, {"id": "0FrYhj3Gu1xlamFh5jKJz", "type": "line", "x": 1536.2606534090905, "y": 1218.383522727273, "width": 0, "height": 262.8203125, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 118646478, "version": 431, "versionNonce": 933720210, "isDeleted": false, "points": [[0, 0], [0, 262.8203125]], "lastCommittedPoint": null}, {"id": "iS-XZzhRH3CFhprUzLn6r", "type": "line", "x": 1542.2780539772725, "y": 1218.074928977273, "width": 0, "height": 262.8203125, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 517200722, "version": 453, "versionNonce": 221452686, "isDeleted": false, "points": [[0, 0], [0, 262.8203125]], "lastCommittedPoint": null}, {"id": "yqLmKs0TNdQEHDNJp__k6", "type": "text", "x": 1156.6491477272725, "y": 1060.1129261363637, "width": 130, "height": 40, "angle": 1.5707963267948966, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 177, "versionNonce": **********, "isDeleted": false, "text": "Provider Specific\nPrivate Hooks", "font": "16px <PERSON>", "textAlign": "center", "baseline": 34}, {"id": "AaenfKnC9j29RCeA0EyPr", "type": "line", "x": 1184.1882102272725, "y": 990.1502130681818, "width": 19.17578125, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 950312082, "version": 117, "versionNonce": 244271630, "isDeleted": false, "points": [[0, 0], [19.17578125, 0]], "lastCommittedPoint": null}, {"id": "T4N-w2KkZkqd5HNSam4rF", "type": "line", "x": 1181.8386008522725, "y": 1176.8110795454547, "width": 19.17578125, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 134665298, "version": 143, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [19.17578125, 0]], "lastCommittedPoint": null}, {"id": "lRiygRl6NILx4OZA0zSgl", "type": "text", "x": 1518.4811789772725, "y": 1315.964488636364, "width": 94, "height": 40, "angle": 1.5707963267948966, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1665507406, "version": 343, "versionNonce": 1716489810, "isDeleted": false, "text": "OnChain\nPublic Hooks", "font": "16px <PERSON>", "textAlign": "center", "baseline": 34}, {"id": "4mtya57_IVob6kqyppHqS", "type": "line", "x": 1521.9618252840905, "y": 1217.309303977273, "width": 19.17578125, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1059044562, "version": 269, "versionNonce": 318027726, "isDeleted": false, "points": [[0, 0], [19.17578125, 0]], "lastCommittedPoint": null}, {"id": "OZw9Wl9s0eXouUyoTFXUG", "type": "line", "x": 1521.577237215909, "y": 1479.95703125, "width": 19.17578125, "height": 0, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 82386062, "version": 284, "versionNonce": 1303245842, "isDeleted": false, "points": [[0, 0], [19.17578125, 0]], "lastCommittedPoint": null}, {"id": "Hjl-IY-0lXFp7XFV0gYUs", "type": "arrow", "x": 713.2150213068189, "y": 1236.5823863636356, "width": 495.80078125, "height": 0, "angle": 0, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 2095221074, "version": 694, "versionNonce": 1556582738, "isDeleted": false, "points": [[0, 0], [-495.80078125, 0]], "lastCommittedPoint": null}, {"id": "U2hEd78EtsQOXYbHgGMY4", "type": "text", "x": 265.84108664772725, "y": 1242.5240589488676, "width": 403, "height": 418, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 526576846, "version": 1349, "versionNonce": **********, "isDeleted": false, "text": "CALLBACK currentUser\n{ acct: FLOW_ACCT_NUMBER\n  verified: true\n  name: NAME\n  avatar: AVATAR\n  email: EMAIL\n  hooks: {\n    authorizations: [{\n     method: \"POST\"\n     endpoint: \"provider.com/authorization\"\n     data: {id: UID}\n  provider: {\n    providerId: PROVIDER_ID\n    uid: UID\n    hooks: HOOKS_URL\n  providers: [{\n    providerId: PROVIDER_ID\n    uid: UID\n    hooks: HOOKS_URL\n  ]\n  \n  ", "font": "16px Cascadia", "textAlign": "left", "baseline": 414}, {"id": "nyHbdI1Y5GBfMmi1zZxfi", "type": "line", "x": 704.96875, "y": 920.3565340909091, "width": 0.****************, "height": 258.**************, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 396, "versionNonce": 392936590, "isDeleted": false, "points": [[0, 0], [-0.****************, 258.**************]], "lastCommittedPoint": [-0.****************, 258.**************]}, {"id": "mjT8vnwL-WYLTTVhQf_u2", "type": "line", "x": 701.1210937499995, "y": 920.************, "width": 0.****************, "height": 258.**************, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 474, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [-0.****************, 258.**************]], "lastCommittedPoint": [-0.****************, 258.**************]}, {"id": "qBQNXmZYHaiAza70BKW9Z", "type": "line", "x": 711.6413352272727, "y": 919.1207386363636, "width": 11.864346590909122, "height": 1.281960227272748, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 374289614, "version": 79, "versionNonce": 1181954958, "isDeleted": false, "points": [[0, 0], [-11.864346590909122, 1.281960227272748]], "lastCommittedPoint": [-11.864346590909122, 1.281960227272748]}, {"id": "ZiyIGma4qyuADMTHFSJkG", "type": "line", "x": 714.4928977272727, "y": 1174.9339488636365, "width": 15.***************, "height": 0.****************, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1949701202, "version": 23, "versionNonce": 1245209550, "isDeleted": false, "points": [[0, 0], [-15.***************, 0.****************]], "lastCommittedPoint": null}, {"id": "Ogp527Unhhs3_237MbyvS", "type": "text", "x": 521.*************, "y": 1032.*************, "width": 307, "height": 40, "angle": 4.**************, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 322, "versionNonce": *********, "isDeleted": false, "text": "If This Request Returns Status 200\nThe Account Is Considered Verified", "font": "16px <PERSON>", "textAlign": "center", "baseline": 34}, {"id": "AgLm-3O6LxnLLTdCrmZzK", "type": "line", "x": 174.*************, "y": 1249.*************, "width": 0.*****************, "height": 360.**************, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 701, "versionNonce": *********, "isDeleted": false, "points": [[0, 0], [-0.*****************, 360.**************]], "lastCommittedPoint": [-0.****************, 258.**************]}, {"id": "JaCl70erlODzBwvSCq6hf", "type": "line", "x": 170.**************, "y": 1249.*************, "width": 0, "height": 363.**************, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 806, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [0, 363.**************]], "lastCommittedPoint": [-0.****************, 258.**************]}, {"id": "Ap4e4vPdboFvWfekpXnP5", "type": "line", "x": 181.27698863636408, "y": 1248.641335227274, "width": 11.864346590909122, "height": 1.281960227272748, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 190435854, "version": 158, "versionNonce": 1899462670, "isDeleted": false, "points": [[0, 0], [-11.864346590909122, 1.281960227272748]], "lastCommittedPoint": [-11.864346590909122, 1.281960227272748]}, {"id": "VD0dZ49pYA5zsWglDlPVy", "type": "line", "x": 182.90696022727315, "y": 1616.0703125000061, "width": 15.***************, "height": 0.****************, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1299094994, "version": 198, "versionNonce": 724476558, "isDeleted": false, "points": [[0, 0], [-15.***************, 0.****************]], "lastCommittedPoint": null}, {"id": "n5NmVSRkP5wnLlL6p5rYH", "type": "text", "x": -54.67329545454572, "y": 1378.16548295455, "width": 332, "height": 100, "angle": 4.**************, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 843, "versionNonce": **********, "isDeleted": false, "text": "Merges the Public (onChain)\nand Private (provider) hooks\n(deep merge last wins, public then private)\nNOTE: Data and format of what\nis exposed to the subscriber may change", "font": "16px <PERSON>", "textAlign": "center", "baseline": 94}, {"id": "Kk39gnjHIQOItun-PEroJ", "type": "line", "x": 93.5390625, "y": 1701.2890625, "width": 777.8203125, "height": 0, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 30, "seed": **********, "version": 252, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [777.8203125, 0]], "lastCommittedPoint": null}, {"id": "BVHNCKtE39YcX7rl-NYFk", "type": "line", "x": 893.0234375, "y": 1676.5390625, "width": 19.234375, "height": 47.828125, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 30, "seed": 473551378, "version": 151, "versionNonce": **********, "isDeleted": false, "points": [[0, 0], [-19.234375, 47.828125]], "lastCommittedPoint": null}, {"id": "44w3pOz5LP8Y2i-OOCDqv", "type": "line", "x": 908.25, "y": 1671.5546875, "width": 19.234375, "height": 47.828125, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 30, "seed": 547271186, "version": 189, "versionNonce": 455012690, "isDeleted": false, "points": [[0, 0], [-19.234375, 47.828125]], "lastCommittedPoint": null}, {"id": "D8AhlJ8rs-11YCcNtGA--", "type": "line", "x": 917.47265625, "y": 1703.1171875, "width": 834.63671875, "height": 0, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 30, "seed": 2092212558, "version": 334, "versionNonce": 1602290894, "isDeleted": false, "points": [[0, 0], [834.63671875, 0]], "lastCommittedPoint": null}, {"id": "Y701Cljlpxs3VNvaw_mdc", "type": "text", "x": 328.625, "y": -155.5546875, "width": 303, "height": 40, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1570471314, "version": 242, "versionNonce": 993528466, "isDeleted": false, "text": "Tell fcl when we authenticate we want\nthe users email if its available", "font": "16px <PERSON>", "textAlign": "left", "baseline": 34}, {"id": "fEsObWhcjK5P6WDvXVkGh", "type": "text", "x": 336.1640625, "y": -69.6640625, "width": 266, "height": 40, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 23237326, "version": 154, "versionNonce": 542094030, "isDeleted": false, "text": "call the callback with the current\nusers info anytime it changes", "font": "16px <PERSON>", "textAlign": "left", "baseline": 34}, {"id": "msFgx92-3AP5XrGwSDBLA", "type": "text", "x": 293.9609375, "y": 46.890625, "width": 407, "height": 40, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 503214286, "version": 275, "versionNonce": 233726930, "isDeleted": false, "text": "subscription callbacks are always called immediately\nwith what we currently know, in this case nothing", "font": "16px <PERSON>", "textAlign": "left", "baseline": 34}, {"id": "x6jn8h4W47vbsNP76m9t7", "type": "text", "x": 301.15625, "y": 304.3203125, "width": 340, "height": 60, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 215811470, "version": 183, "versionNonce": 370821518, "isDeleted": false, "text": "initiate the authentication sequence.\nfcl will inject an iframe into the application\nshowing the handshake interface", "font": "16px <PERSON>", "textAlign": "left", "baseline": 54}, {"id": "efFHHKuH6DUI6OL--FrFK", "type": "text", "x": 115.1328125, "y": -216.4453125, "width": 267, "height": 40, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1880771794, "version": 105, "versionNonce": 2020042194, "isDeleted": false, "text": "consumer of fcl\n(the thing the user wants to use)", "font": "16px <PERSON>", "textAlign": "center", "baseline": 34}, {"id": "fpX9FAuT2DJsHXOS_rwYl", "type": "text", "x": 641.5859375, "y": -191.8359375, "width": 169, "height": 20, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 925850130, "version": 292, "versionNonce": 792939790, "isDeleted": false, "text": "the flow client library", "font": "16px <PERSON>", "textAlign": "center", "baseline": 14}, {"id": "ahlNEXVqBb6yvOSHwMkjm", "type": "text", "x": 868.6171875, "y": -208.4453125, "width": 346, "height": 40, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 873373778, "version": 549, "versionNonce": **********, "isDeleted": false, "text": "the authn and authz provider\n(wallet - where the users private keys live)", "font": "16px <PERSON>", "textAlign": "center", "baseline": 34}, {"id": "10hplQ_DHSsUGighRZYHn", "type": "text", "x": 1255.3671875, "y": -212.2890625, "width": 261, "height": 40, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": **********, "version": 734, "versionNonce": **********, "isDeleted": false, "text": "open source static service\nthat knows what providers exist", "font": "16px <PERSON>", "textAlign": "center", "baseline": 34}, {"id": "NyJ8HxoQf_0obIZfAzAwV", "type": "text", "x": 1585.7734375, "y": -196.234375, "width": 195, "height": 20, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": **********, "version": 900, "versionNonce": **********, "isDeleted": false, "text": "the flow blockchain itself", "font": "16px <PERSON>", "textAlign": "center", "baseline": 14}, {"id": "XohdwJdJwugMecg0mWWrL", "type": "text", "x": 1060.3828125, "y": 159.**********, "width": 337, "height": 200, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 689557134, "version": 1618, "versionNonce": **********, "isDeleted": false, "text": "The user is shown the handshake service\nwhere they can pick which provider they \nwant to use. Once they select the\nprovider they are redirected to\nsaid provider where they will authenticate\nand be redirected back to the handshake \nservice. The handshake service then\ncompletes the handshake by sending an\nevent to the main frame with the details\nprovided by the provider.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 194}, {"id": "PA-OFs620jOJH6zBUwVhQ", "type": "text", "x": 1071.**********, "y": 669, "width": 295, "height": 200, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 300948174, "version": 1178, "versionNonce": **********, "isDeleted": false, "text": "fcl might eventually need to check\na contract on the\nPROVIDER_FLOW_ACCT\nto verify things like the HOOKS_URL.\n\nl6n and nonce must match the ones\nsupplied in the challenge\n\nan exp of \"0\" will be taken as\n\"does not expire\"", "font": "16px <PERSON>", "textAlign": "left", "baseline": 194}, {"id": "fHu_Ej57UFKhjwQiH9caw", "type": "text", "x": 280.**********, "y": 777.**********, "width": 356, "height": 180, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 179421582, "version": 692, "versionNonce": 925722962, "isDeleted": false, "text": "fcl will receive a code and location of where\nto find the hooks for the given provider. It\nshould also receive a flow acct number for\nthe given authenticated user if it exists.\nWith this information we can then fetch the\nprivate hooks (from the provider) and the\nthe public hooks from onChain. If there is no\nflow acct yet, the provider is responsible for\nalso providing the public hooks.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 174}, {"id": "NiVUTrKc8zG3S7jPO8z4E", "type": "text", "x": 1519.**********, "y": 373.26953125, "width": 178, "height": 120, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": **********, "version": 591, "versionNonce": 683364114, "isDeleted": false, "text": "To start with we are \nshipping with an iframe\nflow, there are plans\nfor a more advanced\nredirection style flow\nafter v1.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 114}, {"id": "DNNIurGXx6O6fLDts2gkl", "type": "text", "x": -246.296875, "y": 9.25, "width": 353, "height": 45, "angle": 4.**************, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 30, "seed": 908119694, "version": 482, "versionNonce": 487404558, "isDeleted": false, "text": "AUTHENTICATION", "font": "36px <PERSON>", "textAlign": "left", "baseline": 32}, {"id": "sVyCt94pEcj9uZhTn9vtv", "type": "line", "x": -68.8125, "y": 230.703125, "width": 0, "height": 1421.8046875, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 30, "seed": 674401486, "version": 441, "versionNonce": 1892901330, "isDeleted": false, "points": [[0, 0], [0, 1421.8046875]], "lastCommittedPoint": null}, {"id": "1nyFcQsUWS7SmqC4Athy0", "type": "text", "x": -235.328125, "y": 1855.15234375, "width": 328, "height": 45, "angle": 4.**************, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 30, "seed": 1145858130, "version": 799, "versionNonce": 254090446, "isDeleted": false, "text": "AUTHORIZATION", "font": "36px <PERSON>", "textAlign": "left", "baseline": 32}, {"id": "R6pgiMUijRge1kkbrguoO", "type": "line", "x": -72.9140625, "y": 2050.1328125, "width": 0, "height": 1341.1875, "angle": 0, "strokeColor": "#495057", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 30, "seed": 1128955342, "version": 969, "versionNonce": 574271762, "isDeleted": false, "points": [[0, 0], [0, 1341.1875]], "lastCommittedPoint": null}, {"id": "Xn8RaTdO__vAZuYHGKL5b", "type": "text", "x": 261.1953125, "y": 1913.0625, "width": 459, "height": 152, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 1071355726, "version": 564, "versionNonce": 1195845518, "isDeleted": false, "text": "const resp = await fcl.send([\n  sdk.transaction``,\n  sdk.authorizations([\n    fcl.currentUser().authorization\n  ]),\n  sdk.proposer(fcl.currentUser().proposer),\n  sdk.payer(fcl.currentUser().payerAuthorization)\n])", "font": "16px Cascadia", "textAlign": "left", "baseline": 148}, {"id": "WcLAobcLfNslSdZ7OvolZ", "type": "arrow", "x": 263.1796875, "y": 1912.390625, "width": 448.84375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1960256466, "version": 231, "versionNonce": 122265682, "isDeleted": false, "points": [[0, 0], [448.84375, 0]], "lastCommittedPoint": null}, {"id": "DrttEMCTX96FWcYsEC-QM", "type": "arrow", "x": 739.0546875, "y": 1943.5, "width": 594.2421875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1100047438, "version": 309, "versionNonce": 1132748434, "isDeleted": false, "points": [[0, 0], [594.2421875, 0]], "lastCommittedPoint": null}, {"id": "qJ7Rcxap34blcAk4L1x73", "type": "text", "x": 269.875, "y": 1763.5078125, "width": 298, "height": 140, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 541979470, "version": 308, "versionNonce": 1976392338, "isDeleted": false, "text": "user initiates a transaction\nthat transaction requires the\ncurrent users authorization.\nFCL will look at the current\nusers authorization hooks, broadcast\nto the authorization hooks the\ntransaction to authorize", "font": "16px <PERSON>", "textAlign": "left", "baseline": 134}, {"id": "3IpMkjUGDG9GS1LKr61cL", "type": "text", "x": 786.453125, "y": 1948.5390625, "width": 403, "height": 133, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 201813774, "version": 913, "versionNonce": 2084695826, "isDeleted": false, "text": "HOOK authorization\n{ payload: PAYLOAD (what to actually sign)\n   params: {}\n     hash: HASH (checksum of payload)\nparamHash: PARAM_HASH\n       ix: IX (data used to create payload)\n    roles: [PAYER, PROPOSER, AUTHORIZATION]", "font": "16px Cascadia", "textAlign": "left", "baseline": 129}, {"id": "eUE-R38tGtCxC9UOcIVI5", "type": "arrow", "x": 1324.109375, "y": 2088.8828125, "width": 591.359375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 153273102, "version": 199, "versionNonce": 1425282702, "isDeleted": false, "points": [[0, 0], [-591.359375, 0]], "lastCommittedPoint": null}, {"id": "qC9J4YmM4gkX3IMeBmjov", "type": "text", "x": 813.640625, "y": 2101.765625, "width": 300, "height": 342, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 2044839634, "version": 1531, "versionNonce": 1870666514, "isDeleted": false, "text": "HOOK authorization RESPONSE\n{ status: PENDING\n  signature: null\n  acct: FLOW_ACCT_NUMBER\n  hooks: {\n    info: {\n      method: \"GET\"\n      endpoint: AUTHZ_INFO_URL\n      data: {authzId: AUTHZ_ID}\n    local: [\n      { method: \"IFRAME\"\n        endpoint: FRAME_ENDPOINT\n        width: WIDTH\n        height: HEIGHT\n        background: BG_COLOR\n        \n      \n  ", "font": "16px Cascadia", "textAlign": "left", "baseline": 338}, {"id": "T4oeBL1mIwCVPHO4iKZrJ", "type": "line", "x": 1335.0078125, "y": 1848.889500473485, "width": 0, "height": 933.149562026515, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 1533478926, "version": 2063, "versionNonce": 137336142, "isDeleted": false, "points": [[0, 0], [0, 933.149562026515]], "lastCommittedPoint": null}, {"id": "3OGsOl0sZHOJXVm3y8LXY", "type": "line", "x": 1050.796875, "y": 1691.8828125, "width": 284.1953125, "height": 158.766749526515, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 2005477390, "version": 2654, "versionNonce": 1385622286, "isDeleted": false, "points": [[0, 0], [284.1953125, 158.766749526515]], "lastCommittedPoint": null}, {"id": "5LSTVyBPoxUP_HHqm1IKQ", "type": "arrow", "x": 738.421875, "y": 2408.21875, "width": 588.390625, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 472600718, "version": 80, "versionNonce": 1017686350, "isDeleted": false, "points": [[0, 0], [588.390625, 0]], "lastCommittedPoint": null}, {"id": "Kdv8pHJpP0pY1qAPwbqbF", "type": "text", "x": 807.703125, "y": 2418.9765625, "width": 216, "height": 114, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 1413339282, "version": 1694, "versionNonce": 1049352850, "isDeleted": false, "text": "HOOK info HTTP/GET\n      ?authzId=AUTHZ_ID\n\n        \n      \n  ", "font": "16px Cascadia", "textAlign": "left", "baseline": 110}, {"id": "blrFHdURkWCjSBmlPwz_C", "type": "arrow", "x": 1318.8515625, "y": 2479.1640625, "width": 591.359375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1735255054, "version": 348, "versionNonce": 614616974, "isDeleted": false, "points": [[0, 0], [-591.359375, 0]], "lastCommittedPoint": null}, {"id": "RWAxQGxaplssv98HZXX1y", "type": "text", "x": 795.15625, "y": 2484.734375, "width": 291, "height": 228, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 2044505042, "version": 1672, "versionNonce": 683366866, "isDeleted": false, "text": "HOOK info HTTP/GET RESPONSE\n{ status: PENDING\n  signature: null\n  acct: FLOW_ACCT_NUMBER\n  hooks: {\n    info: {\n      method: \"GET\"\n      endpoint: AUTHZ_INFO_URL\n      data: {authzId: AUTHZ_ID}\n        \n      \n  ", "font": "16px Cascadia", "textAlign": "left", "baseline": 224}, {"id": "g1yWXaOAaP33Q8RH40kkf", "type": "arrow", "x": 729.69921875, "y": 2670.5, "width": 588.390625, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 482511694, "version": 161, "versionNonce": 1686965838, "isDeleted": false, "points": [[0, 0], [588.390625, 0]], "lastCommittedPoint": null}, {"id": "3wEk7jBn-ZuqZMMKemuNg", "type": "text", "x": 798.98046875, "y": 2681.0625, "width": 216, "height": 114, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 1861818514, "version": 1774, "versionNonce": 25492366, "isDeleted": false, "text": "HOOK info HTTP/GET\n      ?authzId=AUTHZ_ID\n\n        \n      \n  ", "font": "16px Cascadia", "textAlign": "left", "baseline": 110}, {"id": "WYl7ztgpr5dhQLjJ8B07m", "type": "arrow", "x": 1316.62109375, "y": 2738.8046875, "width": 591.359375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1798304142, "version": 448, "versionNonce": 761813902, "isDeleted": false, "points": [[0, 0], [-591.359375, 0]], "lastCommittedPoint": null}, {"id": "DcL_FQz9lcO9xEWoXxaI4", "type": "text", "x": 783.23046875, "y": 2745.7578125, "width": 291, "height": 228, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 355219026, "version": 1817, "versionNonce": 1083606354, "isDeleted": false, "text": "HOOK info HTTP/GET RESPONSE\n{ status: SUCCESS\n  signature: SIGNATURE\n  acct: FLOW_ACCT_NUMBER\n  hooks: {\n    info: {\n      method: \"GET\"\n      endpoint: AUTHZ_INFO_URL\n      data: {authzId: AUTHZ_ID}\n        \n      \n  ", "font": "16px Cascadia", "textAlign": "left", "baseline": 224}, {"id": "uzLMXeMktDSo2y-f2jumf", "type": "diamond", "x": 1248.6796875, "y": 2507.818359375, "width": 170.40625, "height": 109.87890625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 1772722898, "version": 391, "versionNonce": 292102414, "isDeleted": false}, {"id": "dVJqOzMa5HaWW6aGEfQ3P", "type": "text", "x": 1295.063920454545, "y": 2536.162997159091, "width": 75, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 326087502, "version": 264, "versionNonce": 1845477074, "isDeleted": false, "text": "user\napproved", "font": "16px Cascadia", "textAlign": "center", "baseline": 34}, {"id": "9Wxw9Kdye3C4a3ybdCf2C", "type": "diamond", "x": 204.203125, "y": 191.326171875, "width": 170.40625, "height": 109.87890625, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 1406251534, "version": 525, "versionNonce": 1701397138, "isDeleted": false}, {"id": "7Z5UyLIWE26Xn9gPVwl_5", "type": "text", "x": 246.08735795454504, "y": 210.170809659091, "width": 84, "height": 57, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 1357168082, "version": 417, "versionNonce": 1218082194, "isDeleted": false, "text": "user\nclicks\n\"sign in\"", "font": "16px Cascadia", "textAlign": "center", "baseline": 53}, {"id": "SP_PXl00bg8Tw7CZJv9gx", "type": "text", "x": 817.171875, "y": 1797.40625, "width": 406, "height": 140, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1267066382, "version": 930, "versionNonce": 336558994, "isDeleted": false, "text": "The hook needs to immediately return the\nstatus of the authorization. This response\nneeds to also include an info hook that fcl can\npoll for an updated status. Optionally it can also\ninclude a array of local hooks. These local hooks\nwill allow the current user to approve things locally\nvia things like an iframe or a browser extension.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 134}, {"id": "Ca1eNJOoLAZUjRT0efJAr", "type": "text", "x": 299.8671875, "y": 2462.7265625, "width": 337, "height": 80, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1242259278, "version": 522, "versionNonce": 639887694, "isDeleted": false, "text": "because the \"knowing\" of the transaction\nstatus is done through a backchannel, this\nshould allow us to completely lockdown this\niframe.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 74}, {"id": "HwC_JIwnKJyv1GD_9tqP9", "type": "arrow", "x": 646.7265625, "y": 2439.4609375, "width": 190.9296875, "height": 157.953125, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1713436178, "version": 127, "versionNonce": 726732626, "isDeleted": false, "points": [[0, 0], [190.9296875, -157.953125]], "lastCommittedPoint": null}, {"id": "G3i7rU2mhmxBDnWjwysoE", "type": "text", "x": 1472.515625, "y": 2523.546875, "width": 218, "height": 100, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 850350158, "version": 188, "versionNonce": 910315214, "isDeleted": false, "text": "fcl polls on a back channel\n(provided by the info hook)\nfor the current status and\nthe signature of the\nauthorization", "font": "16px <PERSON>", "textAlign": "left", "baseline": 94}, {"id": "v7iD8qs6xdnzO1F5-us7W", "type": "arrow", "x": 1461.078125, "y": 2522.3984375, "width": 107.765625, "height": 85.484375, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1418261010, "version": 56, "versionNonce": 1349303634, "isDeleted": false, "points": [[0, 0], [-107.765625, -85.484375]], "lastCommittedPoint": null}, {"id": "ulTg1CxbGTU_jlq8VErm8", "type": "arrow", "x": 1464.7109375, "y": 2585.453125, "width": 117.3046875, "height": 114.78125, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 1773922322, "version": 41, "versionNonce": 979357902, "isDeleted": false, "points": [[0, 0], [-117.3046875, 114.78125]], "lastCommittedPoint": null}, {"id": "xfjvdXmyunnjfu5LPyRUA", "type": "text", "x": 1356.09375, "y": 1986.4296875, "width": 327, "height": 60, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 712589390, "version": 286, "versionNonce": 931274190, "isDeleted": false, "text": "fcl will include enough information\nfor the provider to recreate the payload\nand verify the hash.", "font": "16px <PERSON>", "textAlign": "left", "baseline": 54}, {"id": "Qza6vMiZzFUQR5uaGlp1h", "type": "arrow", "x": 1350.2109375, "y": 1995.9921875, "width": 152.515625, "height": 46.1015625, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": **********, "version": 151, "versionNonce": 229830094, "isDeleted": false, "points": [[0, 0], [-152.515625, 46.1015625]], "lastCommittedPoint": null}, {"id": "VmTKKaZeKqy9c4ubr2W6N", "type": "arrow", "x": 1504.3046875, "y": 2497.6328125, "width": 396.0078125, "height": 278.5703125, "angle": 0, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 178779982, "version": 364, "versionNonce": 46231502, "isDeleted": false, "points": [[0, 0], [-112.8125, -244.8359375], [-396.0078125, -278.5703125]], "lastCommittedPoint": [-396.0078125, -278.5703125]}, {"id": "d_omQJFmqPDltYTA7PQy3", "type": "text", "x": 1400.7890625, "y": 2342.65625, "width": 149, "height": 20, "angle": 1.1645555464937702, "strokeColor": "#e67700", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "roughness": 1, "opacity": 100, "seed": 405322770, "version": 230, "versionNonce": 1604592590, "isDeleted": false, "text": "backchannel to poll", "font": "16px <PERSON>", "textAlign": "left", "baseline": 14}, {"id": "sCx3IIu9HfbFW7qfRD3Lz", "type": "arrow", "x": 267.4921875, "y": 2972.78125, "width": 448.84375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1161635086, "version": 516, "versionNonce": 126493138, "isDeleted": false, "points": [[0, 0], [448.84375, 0]], "lastCommittedPoint": null}, {"id": "Wc5ekixGRrqwIcLPuZ9zr", "type": "text", "x": 280.8359375, "y": 2989.25, "width": 384, "height": 19, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 1454896210, "version": 892, "versionNonce": 654253134, "isDeleted": false, "text": "fcl.transaction(resp).subscribe(callback)", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "_sfg3w7m47XMR4_8vIpjm", "type": "arrow", "x": 709.7578125, "y": 2917.2265625, "width": 453.2109375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1289706066, "version": 490, "versionNonce": 260825358, "isDeleted": false, "points": [[0, 0], [-453.2109375, 0]], "lastCommittedPoint": null}, {"id": "BxQM8jjluMDetjp9Jzhsr", "type": "text", "x": 397.0546875, "y": 2921.0859375, "width": 169, "height": 19, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 527848398, "version": 805, "versionNonce": 1751711442, "isDeleted": false, "text": "fcl.send() returns", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "8GKfI1923PfEAusMB8Vyq", "type": "arrow", "x": 722.9332386363642, "y": 3085.691761363636, "width": 495.80078125, "height": 0, "angle": 0, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 1847975250, "version": 874, "versionNonce": 1821005518, "isDeleted": false, "points": [[0, 0], [-495.80078125, 0]], "lastCommittedPoint": null}, {"id": "yy929ezo92I2Jcv5rOjWU", "type": "line", "x": 612.5426136363628, "y": 3014.998224431818, "width": 394.5390625, "height": 54.5, "angle": 0, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 1437522126, "version": 333, "versionNonce": 28467854, "isDeleted": false, "points": [[0, 0], [-394.5390625, 54.5]], "lastCommittedPoint": null}, {"id": "Qq1hIQTw4JWYoC__BTd4Y", "type": "line", "x": 218.2513612689387, "y": 3069.215021306818, "width": 0, "height": 323.980291193182, "angle": 0, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 1138909970, "version": 1618, "versionNonce": 395772306, "isDeleted": false, "points": [[0, 0], [0, 323.980291193182]], "lastCommittedPoint": null}, {"id": "0yMRCatIazHXvCVVIaLR0", "type": "text", "x": 109.84019886363592, "y": 3156.931463068182, "width": 188, "height": 19, "angle": 4.**************, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "roughness": 0, "opacity": 100, "seed": 741773070, "version": 410, "versionNonce": 715317454, "isDeleted": false, "text": "transaction callback", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "EI6X3C2RLjjuugjHw7Z-r", "type": "text", "x": 349.84375, "y": 3089.0703125, "width": 188, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 1813946830, "version": 990, "versionNonce": 956679442, "isDeleted": false, "text": "CALLBACK transaction\n{ status: RECEIVED", "font": "16px Cascadia", "textAlign": "left", "baseline": 34}, {"id": "zhmjLXcNfFqBZfEKiQe5c", "type": "arrow", "x": 732.9921875, "y": 2995.6796875, "width": 953.9765625, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1249711698, "version": 399, "versionNonce": 783459406, "isDeleted": false, "points": [[0, 0], [953.9765625, 0]], "lastCommittedPoint": null}, {"id": "st15_wioCusHgJ2E5_Ue9", "type": "text", "x": 794.3046875, "y": 3003.46875, "width": 188, "height": 19, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 883500430, "version": 1933, "versionNonce": 1181945746, "isDeleted": false, "text": "FLOW/GET_TRANSACTION", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "Gtl77oNiCZtRLb3snipe3", "type": "arrow", "x": 1680.2890625, "y": 3125.0859375, "width": 946.8203125, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 757225998, "version": 590, "versionNonce": 1107499026, "isDeleted": false, "points": [[0, 0], [-946.8203125, 0]], "lastCommittedPoint": null}, {"id": "GCImaGPjIA1d_h_meLrB5", "type": "text", "x": 784.2265625, "y": 3131.9453125, "width": 272, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 520472786, "version": 2041, "versionNonce": 1336009358, "isDeleted": false, "text": "FLOW/GET_TRANSACTION RESPONSE\n  { status: RECEIVED", "font": "16px Cascadia", "textAlign": "left", "baseline": 34}, {"id": "NTGXxvhF94g8lRBlFN0Gj", "type": "arrow", "x": 732.78515625, "y": 3210.29296875, "width": 953.9765625, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1569466958, "version": 505, "versionNonce": 1919450386, "isDeleted": false, "points": [[0, 0], [953.9765625, 0]], "lastCommittedPoint": null}, {"id": "Skgv5jHJCR5ASTwarEoWl", "type": "text", "x": 794.09765625, "y": 3218.08203125, "width": 188, "height": 19, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 811395474, "version": 2039, "versionNonce": 607589646, "isDeleted": false, "text": "FLOW/GET_TRANSACTION", "font": "16px Cascadia", "textAlign": "left", "baseline": 15}, {"id": "MmPCE991HPZl8hsI24T_0", "type": "arrow", "x": 1679.84765625, "y": 3254.3515625, "width": 946.8203125, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "roughness": 1, "opacity": 100, "seed": 1866686418, "version": 630, "versionNonce": 1715528846, "isDeleted": false, "points": [[0, 0], [-946.8203125, 0]], "lastCommittedPoint": null}, {"id": "vdTbySx1toyPeqIiTfJtI", "type": "text", "x": 783.78515625, "y": 3260.8125, "width": 272, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 553696846, "version": 2109, "versionNonce": 1510280146, "isDeleted": false, "text": "FLOW/GET_TRANSACTION RESPONSE\n  { status: SEALED", "font": "16px Cascadia", "textAlign": "left", "baseline": 34}, {"id": "eid6-y7brbopEetUriCoV", "type": "arrow", "x": 712.228515625, "y": 3343.537286931818, "width": 495.80078125, "height": 0, "angle": 0, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "opacity": 100, "seed": 1083048270, "version": 923, "versionNonce": 847634898, "isDeleted": false, "points": [[0, 0], [-495.80078125, 0]], "lastCommittedPoint": null}, {"id": "dEBoWk9-IXVo658JiIHfZ", "type": "text", "x": 339.1390269886358, "y": 3347.111150568182, "width": 188, "height": 38, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 4, "roughness": 1, "opacity": 100, "seed": 1481114258, "version": 1049, "versionNonce": 1903619090, "isDeleted": false, "text": "CALLBACK transaction\n{ status: SEALED", "font": "16px Cascadia", "textAlign": "left", "baseline": 34}, {"id": "Tl5PuHgh0Nvsdr9lPvJb2", "type": "line", "x": 209.498046875, "y": 55.55078125, "width": 397.509765625, "height": 54.8193359375, "angle": 0, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 4, "roughness": 0, "opacity": 100, "seed": 1951999630, "version": 92, "versionNonce": 1153143506, "isDeleted": false, "points": [[0, 0], [397.509765625, -54.8193359375]], "lastCommittedPoint": null}], "appState": {"viewBackgroundColor": "#ffffff"}}