export const SERVICE_PRAGMA = {
  f_type: "Service",
  f_vsn: "1.0.0",
}

export const IDENTITY_PRAGMA = {
  f_type: "Identity",
  f_vsn: "1.0.0",
}

export const PROVIDER_PRAGMA = {
  f_type: "Provider",
  f_vsn: "1.0.0",
}

export const USER_PRAGMA = {
  f_type: "USER",
  f_vsn: "1.0.0",
}

export const POLLING_RESPONSE_PRAGMA = {
  f_type: "PollingResponse",
  f_vsn: "1.0.0",
}

export const COMPOSITE_SIGNATURE_PRAGMA = {
  f_type: "CompositeSignature",
  f_vsn: "1.0.0",
}

export const OPEN_ID_PRAGMA = {
  f_type: "OpenId",
  f_vsn: "1.0.0",
}
