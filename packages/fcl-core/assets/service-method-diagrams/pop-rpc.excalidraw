{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "rectangle", "version": 603, "versionNonce": 2014138706, "isDeleted": false, "id": "pUp_l9lxr69Ot0vF74iBp", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1053.70703125, "y": 702.982421875, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 583.8359375000001, "height": 88.94531249999997, "seed": 1472544389, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": ["mFodo7CVvji9CuMTQFkGQ"]}, {"type": "rectangle", "version": 375, "versionNonce": 1462281422, "isDeleted": false, "id": "doLY96tTtBiyMShHWADlx", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 537.064453125, "y": 450.865234375, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 378.9140625, "height": 177.87890625000003, "seed": 765761355, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": []}, {"type": "text", "version": 331, "versionNonce": 146495250, "isDeleted": false, "id": "wWCcNk0jRU7XNPNnWQxwy", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 419.76953125, "y": 211.30859375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 66, "height": 19, "seed": 617816645, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "App/FCL", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "text", "version": 596, "versionNonce": 1956043534, "isDeleted": false, "id": "TuphyvaGoKgYofvL5F9sk", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1017.9375, "y": 211.37109375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 38, "height": 19, "seed": 980425131, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "View", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "line", "version": 723, "versionNonce": 1763878098, "isDeleted": false, "id": "GZPY6lLBay4dNxJKh6t9-", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 452.27734375, "y": 234.0078125, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 0, "height": 1545.8109896827361, "seed": 223989419, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [0, 1545.8109896827361]]}, {"type": "line", "version": 772, "versionNonce": 727992654, "isDeleted": false, "id": "gy2-EirFFN9Rs1yv33dxn", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1039.14453125, "y": 236.53125000000006, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 0, "height": 1549.9726593764249, "seed": 1657493451, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [0, 1549.9726593764249]]}, {"type": "text", "version": 344, "versionNonce": 1843284174, "isDeleted": false, "id": "g3OfxKqhShO_gW8ihuy2k", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 832.734375, "y": 159.6416015625, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 216, "height": 19, "seed": 2033659723, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "Service Method: POP/RPC", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "text", "version": 1342, "versionNonce": 1357760466, "isDeleted": false, "id": "cRCXXezMu6O93NnsmMtlw", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 548.46875, "y": 454.5625, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 366, "height": 171, "seed": 264667211, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "{\n  \"f_type\": \"Service\",\n  \"f_vsn\": \"1.0.0\",\n  \"type\": ServiceType, \n  \"method\": \"POP/RPC\",\n  \"endpoint\": \"https://wallet.com/_A_\",\n  \"data\": { \"foo\": \"bar\" },\n  \"params\": { \"omg\": \"rawr\" },\n}", "baseline": 167, "textAlign": "left", "verticalAlign": "top"}, {"type": "arrow", "version": 1939, "versionNonce": 1956190290, "isDeleted": false, "id": "mFodo7CVvji9CuMTQFkGQ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 459.27442534082866, "y": 708.6711316997614, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 575.9935136992426, "height": 0, "seed": 207407941, "groupIds": ["mI9O7RDP-tPOgCFotC0Sz"], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "pVy0_Vt7wLNHak-3XpKct", "focus": -2.08231508423564, "gap": 10.281993300238582}, "endBinding": {"elementId": "pUp_l9lxr69Ot0vF74iBp", "focus": 0.8720852248450665, "gap": 18.43909220992873}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [575.9935136992426, 0]]}, {"type": "text", "version": 555, "versionNonce": 258002382, "isDeleted": false, "id": "pVy0_Vt7wLNHak-3XpKct", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 457.765625, "y": 718.953125, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 459, "height": 19, "seed": 974936005, "groupIds": ["mI9O7RDP-tPOgCFotC0Sz"], "strokeSharpness": "sharp", "boundElementIds": ["mFodo7CVvji9CuMTQFkGQ"], "fontSize": 16, "fontFamily": 3, "text": "Render `https://wallet.com/_A_?omg=rawr` as popup", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "text", "version": 315, "versionNonce": 775306770, "isDeleted": false, "id": "S8hRM8CDbjBy1msJQBXUT", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 461.296875, "y": 234.8359375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 516, "height": 133, "seed": 1169787467, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "import {config} from \"@onflow/fcl\"\n\nconfig({\n  \"app.detail.title\": \"My Awesome App\",\n  \"app.detail.icon\": \"https://app.com/assets/icon.jpg\",\n  \"service.OpenID.scopes\": \"email!\",\n})", "baseline": 129, "textAlign": "left", "verticalAlign": "top"}, {"type": "text", "version": 579, "versionNonce": 635560974, "isDeleted": false, "id": "qD9xJl1tdtaGnKb076C85", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1057.98828125, "y": 705.76953125, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 572, "height": 76, "seed": 1407251013, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": ["mFodo7CVvji9CuMTQFkGQ"], "fontSize": 16, "fontFamily": 3, "text": "import {WalletUtils} from \"@onflow/fcl\"\n\nWalletUtils.onMsgFromFCL(\"FCL:VIEW:READY:RESPONSE\", callback)\nWalletUtils.sendMsgToFCL(\"FCL:VIEW:READY\")", "baseline": 72, "textAlign": "left", "verticalAlign": "top"}, {"type": "arrow", "version": 524, "versionNonce": 212380626, "isDeleted": false, "id": "XHwC7Fsg0ku28pJIQeHXX", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1039.4156656271816, "y": 877.13671875, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 583.5457218851187, "height": 0, "seed": 2055359205, "groupIds": ["hkRFVCY3u_l0jWCjI8A06"], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "iJSRIo1_cDlQLtOn1fYl6", "focus": 1.1167763157894735, "gap": 6.439103127181625}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-583.5457218851187, 0]]}, {"type": "text", "version": 488, "versionNonce": 1885745742, "isDeleted": false, "id": "iJSRIo1_cDlQLtOn1fYl6", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 788.9765625, "y": 880.46484375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 244, "height": 57, "seed": 1852876837, "groupIds": ["hkRFVCY3u_l0jWCjI8A06"], "strokeSharpness": "sharp", "boundElementIds": ["XHwC7Fsg0ku28pJIQeHXX"], "fontSize": 16, "fontFamily": 3, "text": "{\n  \"type\": \"FCL:VIEW:READY\"\n}", "baseline": 53, "textAlign": "left", "verticalAlign": "top"}, {"type": "arrow", "version": 575, "versionNonce": 1443324306, "isDeleted": false, "id": "o3NY2Deu9m4VQwicKDgPc", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 455.60388297963584, "y": 1013.7656250000001, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 582.8264913860365, "height": 0, "seed": 2101489195, "groupIds": ["Ik0L6Z4_VlST0N-2z6VQV"], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "MtdfkG87ukUkNfzon_9Op", "focus": -1.0214057662538694, "gap": 4.989867020364159}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [582.8264913860365, 0]]}, {"type": "text", "version": 746, "versionNonce": 2043066510, "isDeleted": false, "id": "MtdfkG87ukUkNfzon_9Op", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 460.59375, "y": 1017.22265625, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 459, "height": 323, "seed": 569460709, "groupIds": ["Ik0L6Z4_VlST0N-2z6VQV"], "strokeSharpness": "sharp", "boundElementIds": ["o3NY2Deu9m4VQwicKDgPc"], "fontSize": 16, "fontFamily": 3, "text": "{\n  \"type\": \"FCL:VIEW:READY:RESPONSE\",\n  \"body\": BodyType,\n  \"service\": {\n    \"params\": { \"omg\": \"rawr\" },\n    \"data\": { \"foo\": \"bar\" }\n  },\n  \"config\": {\n    \"service\": {\n      \"OpenID.scopes\": \"email!\",\n    },\n    \"app\": {\n      \"title\": \"My Awesome App\",\n      \"icon\": \"https://app.com/assets/icon.jpeg\",\n    }\n  }\n}", "baseline": 319, "textAlign": "left", "verticalAlign": "top"}, {"type": "rectangle", "version": 445, "versionNonce": 1971937106, "isDeleted": false, "id": "tOR3r8-ktC1NTHzDZox-Z", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1123.6640625, "y": 221.30859375, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 500.21874999999994, "height": 171.82812500000003, "seed": 1547570789, "groupIds": ["TpzbWBW_qlEU-Hi5j9Dw7"], "strokeSharpness": "sharp", "boundElementIds": []}, {"type": "text", "version": 809, "versionNonce": 163346126, "isDeleted": false, "id": "RENTSbmLHkw8L4P6Er97V", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1133.3828125, "y": 229.09375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 478, "height": 152, "seed": 2115574885, "groupIds": ["TpzbWBW_qlEU-Hi5j9Dw7"], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "ServiceTypes, BodyTypes and expected ReturnValues\n\n   ServiceType | BodyTypes   | ReturnValue\n---------------+-------------+---------------------\n         authn | ---         | AuthnResponse\n         authz | Signable    | CompositeSignature\n     pre-authz | PreSignable | PreAuthzResponse\nuser-signature | Signable    | [CompositeSignature]", "baseline": 148, "textAlign": "left", "verticalAlign": "top"}, {"type": "rectangle", "version": 577, "versionNonce": 1468376338, "isDeleted": false, "id": "sBtMUDtXKIfllfl-9WUfC", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1082.9218749999995, "y": 1401.093750000001, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 460.531977867585, "height": 391.37109374999994, "seed": 1727540043, "groupIds": ["s38pqtF6zdwbpuo0GwZ4q"], "strokeSharpness": "sharp", "boundElementIds": []}, {"type": "text", "version": 545, "versionNonce": 148925710, "isDeleted": false, "id": "PsEZcFM0IZw13zeSuy3Uc", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1095.0624999999995, "y": 1404.109375000001, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 441, "height": 380, "seed": 1976198821, "groupIds": ["s38pqtF6zdwbpuo0GwZ4q"], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "import {WalletUtiles} from \"@onflow/fcl\"\n\n// Cancel\nWalletUtils.sendMsgToFCL(\"FCL:VIEW:CLOSE\")\n\n// Approved\nWalletUtils.sendMsgToFCL(\"FCL:VIEW:RESPONSE\", {\n  \"f_type\": \"PollingResponse\",\n  \"f_vsn\": \"1.0.0\",\n  \"status\": \"APPROVED\",\n  \"data\": ReturnValue\n})\n\n// Declined\nWalletUtils.sendMsgToFCL(\"FCL:VIEW:RESPONSE\", {\n  \"f_type\": \"PollingResponse\",\n  \"f_vsn\": \"1.0.0\",\n  \"status\": \"DECLINED\",\n  \"reason\": \"The user didn't want to do it...\"\n})", "baseline": 376, "textAlign": "left", "verticalAlign": "top"}, {"type": "arrow", "version": 146, "versionNonce": 1165177554, "isDeleted": false, "id": "qqMnX6ggiFPgEfmLjWxL1", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1032.483774543553, "y": 1456.839843750001, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 572.9056495435534, "height": 0, "seed": 341230117, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "2P_cD3a0Hs2ZHWSEOm817", "focus": 1.227091165413534, "gap": 15.1015625}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-572.9056495435534, 0]]}, {"type": "text", "version": 333, "versionNonce": 1688968014, "isDeleted": false, "id": "2P_cD3a0Hs2ZHWSEOm817", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 742.3945312499995, "y": 1471.941406250001, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 281, "height": 133, "seed": 816216837, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": ["qqMnX6ggiFPgEfmLjWxL1"], "fontSize": 16, "fontFamily": 3, "text": "{\n  \"f_type\": \"PollingResponse\",\n  \"f_vsn\": \"1.0.0\",\n  \"type\": \"FCL:VIEW:RESPONSE\",\n  \"status\": \"APPROVED\",\n  \"data\": ReturnValue\n}", "baseline": 129, "textAlign": "left", "verticalAlign": "top"}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}}