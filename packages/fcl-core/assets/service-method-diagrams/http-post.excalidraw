{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "rectangle", "version": 373, "versionNonce": 338338514, "isDeleted": false, "id": "doLY96tTtBiyMShHWADlx", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 412.064453125, "y": 450.865234375, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 378.9140625, "height": 177.87890625000003, "seed": 765761355, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": []}, {"type": "text", "version": 329, "versionNonce": 325266254, "isDeleted": false, "id": "wWCcNk0jRU7XNPNnWQxwy", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 294.76953125, "y": 211.30859375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 66, "height": 19, "seed": 617816645, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "App/FCL", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "text", "version": 594, "versionNonce": 674396306, "isDeleted": false, "id": "TuphyvaGoKgYofvL5F9sk", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 991.8102678571429, "y": 214.26729910714286, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 94, "height": 38, "seed": 980425131, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "Wallet/API\n", "baseline": 34, "textAlign": "left", "verticalAlign": "top"}, {"type": "line", "version": 721, "versionNonce": 505874382, "isDeleted": false, "id": "GZPY6lLBay4dNxJKh6t9-", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 327.64564732142856, "y": 234.0078125, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 0, "height": 2418.227889550592, "seed": 223989419, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [0, 2418.227889550592]]}, {"type": "line", "version": 770, "versionNonce": 1131652882, "isDeleted": false, "id": "gy2-EirFFN9Rs1yv33dxn", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1039.14453125, "y": 236.53125000000006, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 0, "height": 2427.8363767384853, "seed": 1657493451, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [0, 2427.8363767384853]]}, {"type": "text", "version": 320, "versionNonce": 1250897870, "isDeleted": false, "id": "g3OfxKqhShO_gW8ihuy2k", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 832.734375, "y": 159.96875, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 234, "height": 19, "seed": 2033659723, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "Service Method: HTTP/POST", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "text", "version": 1319, "versionNonce": 1293351954, "isDeleted": false, "id": "cRCXXezMu6O93NnsmMtlw", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 423.83705357142867, "y": 454.5625, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 366, "height": 171, "seed": 264667211, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "{\n  \"f_type\": \"Service\",\n  \"f_vsn\": \"1.0.0\",\n  \"type\": ServiceType, \n  \"method\": \"HTTP/POST\",\n  \"endpoint\": \"https://wallet.com/_A_\",\n  \"data\": { \"foo\": \"bar\" },\n  \"params\": { \"omg\": \"rawr\" },\n}", "baseline": 167, "textAlign": "left", "verticalAlign": "top"}, {"type": "text", "version": 313, "versionNonce": 417600590, "isDeleted": false, "id": "S8hRM8CDbjBy1msJQBXUT", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 336.296875, "y": 234.8359375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 516, "height": 133, "seed": 1169787467, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "import {config} from \"@onflow/fcl\"\n\nconfig({\n  \"app.detail.title\": \"My Awesome App\",\n  \"app.detail.icon\": \"https://app.com/assets/icon.jpg\",\n  \"service.OpenID.scopes\": \"email!\",\n})", "baseline": 129, "textAlign": "left", "verticalAlign": "top"}, {"type": "rectangle", "version": 443, "versionNonce": 1355910926, "isDeleted": false, "id": "tOR3r8-ktC1NTHzDZox-Z", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1123.6640625, "y": 221.30859375, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 500.21874999999994, "height": 171.82812500000003, "seed": 1547570789, "groupIds": ["TpzbWBW_qlEU-Hi5j9Dw7"], "strokeSharpness": "sharp", "boundElementIds": []}, {"type": "text", "version": 807, "versionNonce": 169685202, "isDeleted": false, "id": "RENTSbmLHkw8L4P6Er97V", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1133.3828125, "y": 229.09375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 478, "height": 152, "seed": 2115574885, "groupIds": ["TpzbWBW_qlEU-Hi5j9Dw7"], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "ServiceTypes, BodyTypes and expected ReturnValues\n\n   ServiceType | BodyType    | ReturnValue\n---------------+-------------+---------------------\n         authn | ---         | AuthnResponse\n         authz | Signable    | CompositeSignature\n     pre-authz | PreSignable | PreAuthzResponse\nuser-signature | Signable    | [CompositeSignature]", "baseline": 148, "textAlign": "left", "verticalAlign": "top"}, {"type": "text", "version": 973, "versionNonce": 356106770, "isDeleted": false, "id": "ccmKZf8ImiNbmVmoUdu89", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1679.5541294642849, "y": 211.96755203363227, "strokeColor": "#862e9c", "backgroundColor": "transparent", "width": 38, "height": 19, "seed": 1992256210, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "View", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "line", "version": 1271, "versionNonce": 1070864338, "isDeleted": false, "id": "pgNGyMHiId3ZIrPXhHaAR", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1700.3989955357133, "y": 234.66677078363227, "strokeColor": "#862e9c", "backgroundColor": "transparent", "width": 0, "height": 2416.3547968355406, "seed": 1734271822, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [0, 2416.3547968355406]]}, {"id": "pvU9mNYKpnlmvgt8RQjVu", "type": "arrow", "x": 335.89356569608776, "y": 703.064174107143, "width": 696.4829903954293, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["BJEixf1iYgvauBNe_-YX2"], "strokeSharpness": "round", "seed": 227842002, "version": 616, "versionNonce": 1159895054, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [696.4829903954293, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "meRKKpVnYEjnay7ATEHed", "focus": -1.2005991541353331, "gap": 3.811383928571331}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "meRKKpVnYEjnay7ATEHed", "type": "text", "x": 333.0061383928566, "y": 706.8755580357143, "width": 384, "height": 38, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["BJEixf1iYgvauBNe_-YX2"], "strokeSharpness": "sharp", "seed": 369660242, "version": 376, "versionNonce": 100480978, "isDeleted": false, "boundElementIds": ["pvU9mNYKpnlmvgt8RQjVu"], "text": "http POST https://wallet.com/_A_?omg=rawr\n     BODY {...BodyType}", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 34}, {"id": "goiBksgB2pTwoXPsezR5-", "type": "arrow", "x": 1043.6843165147914, "y": 881.8922991071431, "width": 708.317687050506, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["HetzAfHUD7tDp1J6UK6m_"], "strokeSharpness": "round", "seed": 414757646, "version": 475, "versionNonce": 1299211854, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [-708.317687050506, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "6gbdp_X3azm8N_Bx9PzoM", "focus": 1.0408374877410902, "gap": 8.922991071428214}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "6gbdp_X3azm8N_Bx9PzoM", "type": "text", "x": 374.2896205357139, "y": 890.8152901785713, "width": 656, "height": 437, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["HetzAfHUD7tDp1J6UK6m_"], "strokeSharpness": "sharp", "seed": 1490619154, "version": 1332, "versionNonce": 972197266, "isDeleted": false, "boundElementIds": ["goiBksgB2pTwoXPsezR5-"], "text": "{\n  \"f_type\": \"PollingResponse\",\n  \"f_vsn\": \"1.0.0\",\n  \"status\": \"PENDING\",\n  \"updates\": {\n    \"f_type\": \"Service\",\n    \"f_vsn\": \"1.0.0\",\n    \"type\": \"back-channel-rpc\",\n    \"endpoint\": \"https://app.com/tx/updates/endpoint\",\n    \"method\": \"HTTP/GET\", // \"HTTP/POST\" | \"HTTP/GET\",\n    \"data\": { \"woot\": \"boot\" }, // body of HTTP/POST\n    \"params\": { \"zomg\": \"pew\" },\n  },\n  \"local\": { // optional\n    \"f_type\": \"Service\",\n    \"f_vsn\": \"1.0.0\",\n    \"type\": \"local-view\",\n    \"method\": \"VIEW/IFRAME\", // \"VIEW/POP\" | \"VIEW/TAB\" | \"VIEW/IFRAME\"\n    \"endpoint\" \"https://app.com/tx/approval/endpoint\",\n    \"data\": { \"woot\": \"boot\" },\n    \"params\": { \"zomg\": \"pew\" },\n  },\n}", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 433}, {"id": "nXcjEQV_1zIljBACiCBbQ", "type": "arrow", "x": 333.1183228581119, "y": 1365.32421875, "width": 1364.067503034744, "height": 0, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 687806418, "version": 253, "versionNonce": 1708379794, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [1364.067503034744, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "0217_iMIVmw5Td5Jr_IN6", "focus": -1.4053101503759535, "gap": 3.8504464285715585}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "0217_iMIVmw5Td5Jr_IN6", "type": "text", "x": 334.2170758928564, "y": 1369.1746651785716, "width": 703, "height": 19, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 1566002194, "version": 167, "versionNonce": 1828952974, "isDeleted": false, "boundElementIds": ["nXcjEQV_1zIljBACiCBbQ"], "text": "Render VIEW https://app.com/tx/approval/endpoint?zomg=pew as method(iframe)", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15}, {"id": "kp_hrClqZKnQIp0t6Fnrb", "type": "arrow", "x": 332.80524553571416, "y": 1444.436941964286, "width": 698.8727678571428, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 636766798, "version": 117, "versionNonce": 684305102, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [698.8727678571428, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "HFLt7Pr186-t2UXAq0TV4", "focus": -1.6050281954887253, "gap": 5.74776785714289}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "HFLt7Pr186-t2UXAq0TV4", "type": "text", "x": 336.125558035714, "y": 1450.184709821429, "width": 497, "height": 19, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 1762610382, "version": 105, "versionNonce": 1645588754, "isDeleted": false, "boundElementIds": ["kp_hrClqZKnQIp0t6Fnrb"], "text": "http GET https://app.com/tx/updates/endpoint?zomg=pew", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15}, {"id": "TugzorT6e20PkkSBr9P3h", "type": "arrow", "x": 1031.8374193590034, "y": 1512.1489955357147, "width": 688.2860807884815, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 947212882, "version": 511, "versionNonce": 1550547150, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [-688.2860807884815, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "EbXoOC1rg5YiPck-Cg0Hq", "focus": 1.0613000134264263, "gap": 13.283289894717996}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "EbXoOC1rg5YiPck-Cg0Hq", "type": "text", "x": 512.5541294642854, "y": 1520.3018973214294, "width": 506, "height": 266, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 1939255886, "version": 290, "versionNonce": 213893906, "isDeleted": false, "boundElementIds": ["TugzorT6e20PkkSBr9P3h"], "text": "{\n  \"f_type\": \"PollingResponse\",\n  \"f_vsn\": \"1.0.0\",\n  \"status\": \"PENDING\",\n  \"updates\": {\n    \"f_type\": \"Service\",\n    \"f_vsn\": \"1.0.0\",\n    \"type\": \"back-channel-rpc\",\n    \"endpoint\": \"https://app.com/tx/updates/endpoint\",\n    \"method\": \"HTTP/GET\", // \"HTTP/POST\" | \"HTTP/GET\",\n    \"data\": { \"woot\": \"boot\" }, // body of HTTP/POST\n    \"params\": { \"zomg\": \"pew\" },\n  },\n}", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 262}, {"id": "uSClf3BZErW3vAw7qBEkM", "type": "arrow", "x": 1571.5222052550273, "y": 1858.2354027726096, "width": 121.00960278068669, "height": 0.4258695488185822, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 79856402, "version": 238, "versionNonce": 754574738, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [121.00960278068669, 0.4258695488185822]], "lastCommittedPoint": null, "startBinding": {"elementId": "px4AOTe4JsFHw9MHW63Wr", "focus": 0.04382048872187289, "gap": 5.219191862170419}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "q13cnvjmUM3cjbfkI8jzA", "type": "text", "x": 229.88671874999963, "y": 1596.0106026785706, "width": 38, "height": 19, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["IxDaDMcSo_GQi-HDfbVG4"], "strokeSharpness": "sharp", "seed": 1371875022, "version": 124, "versionNonce": 590490706, "isDeleted": false, "boundElementIds": null, "text": "LOOP", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15}, {"id": "0ftGanngPJe3yf4XCxuJU", "type": "line", "x": 287.7885044642853, "y": 1442.7293526785706, "width": 0, "height": 360.4854910714289, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["IxDaDMcSo_GQi-HDfbVG4"], "strokeSharpness": "round", "seed": 749051986, "version": 36, "versionNonce": 26409422, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [0, 360.4854910714289]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "VkDSg1H9RmE4p7RfcC_Dn", "type": "line", "x": 287.23046874999966, "y": 1443.6110491071422, "width": 18.922991071428555, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["IxDaDMcSo_GQi-HDfbVG4"], "strokeSharpness": "round", "seed": 624131918, "version": 18, "versionNonce": 1351406098, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [18.922991071428555, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "l2r2X4jf9sO1DNxVhrhMg", "type": "line", "x": 287.65457589285677, "y": 1804.397879464285, "width": 28.705357142857224, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["IxDaDMcSo_GQi-HDfbVG4"], "strokeSharpness": "round", "seed": 520499858, "version": 15, "versionNonce": 1760464910, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [28.705357142857224, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "ZPOVTwce_dOkbMSKAZlOc", "type": "line", "x": 274.26171874999966, "y": 1604.1077008928564, "width": 14.196428571428555, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["IxDaDMcSo_GQi-HDfbVG4"], "strokeSharpness": "round", "seed": 558246610, "version": 15, "versionNonce": 1326235602, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [14.196428571428555, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "px4AOTe4JsFHw9MHW63Wr", "type": "text", "x": 1182.3030133928569, "y": 1847.5954241071418, "width": 384, "height": 19, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 1523119506, "version": 464, "versionNonce": 607024270, "isDeleted": false, "boundElementIds": ["uSClf3BZErW3vAw7qBEkM"], "text": "USER INTERACTION APPROVES SERVICE REQUEST", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15}, {"type": "arrow", "version": 393, "versionNonce": 1948720466, "isDeleted": false, "id": "EWPCysYpkOsNA7VR_51Ou", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 332.49274553571405, "y": 1987.286272321427, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 698.8727678571428, "height": 0, "seed": 1551611214, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "jnw9NcDG4qfKgwJeYBNeZ", "focus": -1.605028195488749, "gap": 5.747767857143117}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [698.8727678571428, 0]]}, {"type": "text", "version": 256, "versionNonce": 459380942, "isDeleted": false, "id": "jnw9NcDG4qfKgwJeYBNeZ", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 335.44475446428527, "y": 1993.0340401785702, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 497, "height": 19, "seed": 37719698, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": ["EWPCysYpkOsNA7VR_51Ou"], "fontSize": 16, "fontFamily": 3, "text": "http GET https://app.com/tx/updates/endpoint?zomg=pew", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "arrow", "version": 868, "versionNonce": 2008785682, "isDeleted": false, "id": "mqh21vSnuumpPwVUt0p_9", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1029.418875631516, "y": 2050.954241071428, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 694.9810008471857, "height": 0, "seed": 1236898002, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "_EhFX6jLqQlumRQq0dlzg", "focus": 1.0957471804511347, "gap": 8.890040310568224}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-694.9810008471857, 0]]}, {"type": "text", "version": 533, "versionNonce": 764497678, "isDeleted": false, "id": "_EhFX6jLqQlumRQq0dlzg", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 739.5288353209477, "y": 2056.4118303571427, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 281, "height": 114, "seed": 1963072846, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": ["mqh21vSnuumpPwVUt0p_9"], "fontSize": 16, "fontFamily": 3, "text": "{\n  \"f_type\": \"PollingResponse\",\n  \"f_vsn\": \"1.0.0\",\n  \"status\": \"APPROVED\",\n  \"data\": ReturnValue\n}", "baseline": 110, "textAlign": "left", "verticalAlign": "top"}, {"type": "arrow", "version": 359, "versionNonce": 1401643858, "isDeleted": false, "id": "TiKzIfkRF5lkFQK31JND2", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1587.456915076456, "y": 2187.2416294642853, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "width": 102.44933492354403, "height": 0, "seed": 1225038286, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "NSKk16J0dklbZp723xFAc", "focus": 0.0438204887218729, "gap": 5.219191862170192}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [102.44933492354403, 0]]}, {"type": "text", "version": 506, "versionNonce": 130855630, "isDeleted": false, "id": "NSKk16J0dklbZp723xFAc", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1188.2377232142858, "y": 2177.3253348214275, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "width": 394, "height": 19, "seed": 713372178, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": ["TiKzIfkRF5lkFQK31JND2"], "fontSize": 16, "fontFamily": 3, "text": "EXTERNAL INTERACTION DECLINES SERVICE CALL", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "arrow", "version": 556, "versionNonce": 596512338, "isDeleted": false, "id": "dsgLgRv7-hGW6LGq_WyIy", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 337.03515625000017, "y": 2355.2458147321418, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 698.8727678571428, "height": 0, "seed": 1559268302, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "eozBaDjhl7bl9Wb_52yX2", "focus": -1.6050281954887489, "gap": 5.747767857143117}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [698.8727678571428, 0]]}, {"type": "text", "version": 338, "versionNonce": 786315214, "isDeleted": false, "id": "eozBaDjhl7bl9Wb_52yX2", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 339.9871651785713, "y": 2360.993582589285, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 497, "height": 19, "seed": 285827090, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": ["dsgLgRv7-hGW6LGq_WyIy"], "fontSize": 16, "fontFamily": 3, "text": "http GET https://app.com/tx/updates/endpoint?zomg=pew", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"type": "arrow", "version": 951, "versionNonce": 1595700242, "isDeleted": false, "id": "0KYT_pccrbXHsmDvRc7C6", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1033.9612863458024, "y": 2418.539899553571, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 694.9810008471857, "height": 0, "seed": 1724506638, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "ame2VZzxjweqrMDB40km4", "focus": 1.1037750626566554, "gap": 10.307451024854345}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-694.9810008471857, 0]]}, {"type": "text", "version": 697, "versionNonce": 102895118, "isDeleted": false, "id": "ame2VZzxjweqrMDB40km4", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 592.653835320948, "y": 2424.4550781250005, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 431, "height": 114, "seed": 751275474, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": ["0KYT_pccrbXHsmDvRc7C6"], "fontSize": 16, "fontFamily": 3, "text": "{\n  \"f_type\": \"PollingResponse\",\n  \"f_vsn\": \"1.0.0\",\n  \"status\": \"DECLINED\",\n  \"reason\": \"The user didn't want to do it...\"\n}", "baseline": 110, "textAlign": "left", "verticalAlign": "top"}, {"id": "Rq2VYrSlBVIt9YtHmuNTf", "type": "arrow", "x": 1698.7148437500002, "y": 1898.1422991071422, "width": 654.1294642857144, "height": 0, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 178499278, "version": 75, "versionNonce": 782706962, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [-654.1294642857144, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "aJZkW8PwKFNElq2a4B-eG", "type": "text", "x": 1284.6356026785713, "y": 1904.6434151785709, "width": 225, "height": 19, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 281210002, "version": 138, "versionNonce": 1367943438, "isDeleted": false, "boundElementIds": null, "text": "APPROVES SERVICE REQUEST", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15}, {"type": "arrow", "version": 161, "versionNonce": 1170486994, "isDeleted": false, "id": "tqGejRrekqGmHpxVORAGr", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1693.2963169642865, "y": 2221.9235491071418, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "width": 654.1294642857144, "height": 0, "seed": 1173598546, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-654.1294642857144, 0]]}, {"type": "text", "version": 233, "versionNonce": 282908494, "isDeleted": false, "id": "vsAUKDrl-yoJZK45NlzNA", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1279.217075892857, "y": 2228.050781249999, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "width": 225, "height": 19, "seed": 156779726, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "fontSize": 16, "fontFamily": 3, "text": "DECLINES SERVICE REQUEST", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"id": "2l8s1WXfRoNJvn8pV-qAT", "type": "text", "x": 1131.337611607143, "y": 401.8141741071414, "width": 338, "height": 19, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 427396242, "version": 213, "versionNonce": 1823437778, "isDeleted": false, "boundElementIds": null, "text": "** THINGS IN THIS COLOR ARE OPTIONAL", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15}, {"type": "arrow", "version": 326, "versionNonce": 47482066, "isDeleted": false, "id": "vrhKm_s9FT000zAwzPpE_", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 337.279401841186, "y": 2608.0535714285697, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "width": 1358.9689240516714, "height": 0, "seed": 142485330, "groupIds": [], "strokeSharpness": "round", "boundElementIds": [], "startBinding": {"elementId": "I7NvNbuLGTAyrU1J2b0Nj", "focus": -1.444666353383438, "gap": 4.224330357142662}, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [1358.9689240516714, 0]]}, {"type": "text", "version": 218, "versionNonce": 641755470, "isDeleted": false, "id": "I7NvNbuLGTAyrU1J2b0Nj", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 333.27957589285774, "y": 2612.2779017857124, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "width": 525, "height": 19, "seed": 1666723534, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": ["vrhKm_s9FT000zAwzPpE_"], "fontSize": 16, "fontFamily": 3, "text": "Close VIEW https://app.com/tx/approval/endpoint?zomg=pew", "baseline": 15, "textAlign": "left", "verticalAlign": "top"}, {"id": "QGPWF6lQ3Lqq6D-MM5tRS", "type": "line", "x": 300.3136160714284, "y": 1968.0273437499995, "width": 0, "height": 235.359375, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["bcC7Yy1WL2LxGqQmgco5W"], "strokeSharpness": "round", "seed": 1815031246, "version": 176, "versionNonce": 1972738130, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [0, 235.359375]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "fUn5BO819itbACQI93865", "type": "line", "x": 301.4464285714284, "y": 1968.4374999999995, "width": 17.390625, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["bcC7Yy1WL2LxGqQmgco5W"], "strokeSharpness": "round", "seed": 1267356242, "version": 14, "versionNonce": 174869966, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [17.390625, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "QZJcoMNHUzIwRPakO3Vsp", "type": "line", "x": 300.2901785714284, "y": 2202.5742187499995, "width": 20.26171875, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["bcC7Yy1WL2LxGqQmgco5W"], "strokeSharpness": "round", "seed": 1220444494, "version": 38, "versionNonce": 1537392146, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [20.26171875, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "xZsaBFYQfBcyiRJwk5q3y", "type": "line", "x": 299.9972098214284, "y": 2084.0078124999995, "width": 18.41015625, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["bcC7Yy1WL2LxGqQmgco5W"], "strokeSharpness": "round", "seed": 1949442578, "version": 27, "versionNonce": 863855630, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [-18.41015625, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"type": "line", "version": 267, "versionNonce": 1336617934, "isDeleted": false, "id": "QbBwRYiSlmLl_H5F1dE7X", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 301.4835379464284, "y": 2339.1562499999995, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 0, "height": 235.359375, "seed": 1485122514, "groupIds": ["LufmXpA_fNqsMwGoG1a29"], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [0, 235.359375]]}, {"type": "line", "version": 105, "versionNonce": 885571602, "isDeleted": false, "id": "teXwzjfksaQOi9rlbmVnr", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 302.6163504464284, "y": 2339.5664062499995, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 17.390625, "height": 0, "seed": 1049735758, "groupIds": ["LufmXpA_fNqsMwGoG1a29"], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [17.390625, 0]]}, {"type": "line", "version": 129, "versionNonce": 1764786702, "isDeleted": false, "id": "RqIxRQuNE7ROfIU4xU_qD", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 301.4601004464284, "y": 2573.7031249999995, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 20.26171875, "height": 0, "seed": 1918099858, "groupIds": ["LufmXpA_fNqsMwGoG1a29"], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [20.26171875, 0]]}, {"type": "line", "version": 118, "versionNonce": 1637543378, "isDeleted": false, "id": "LzmrQIkmkDOYt4DUxggnq", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 301.1671316964284, "y": 2455.1367187499995, "strokeColor": "#000000", "backgroundColor": "#ced4da", "width": 18.41015625, "height": 0, "seed": 1988245646, "groupIds": ["LufmXpA_fNqsMwGoG1a29"], "strokeSharpness": "round", "boundElementIds": [], "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [-18.41015625, 0]]}, {"id": "TQyIkMTQlMtpOkAG1NvA1", "type": "line", "x": 185.13392857142838, "y": 1602.2070312499995, "width": 0, "height": 859.796875, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 392258450, "version": 72, "versionNonce": 1976090958, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [0, 859.796875]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "9qzClc4VVSNxN-Ligd-5V", "type": "line", "x": 186.07924107142838, "y": 1601.6445312499995, "width": 25.65625, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 957956754, "version": 10, "versionNonce": 1583362190, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [25.65625, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "JIokfUK5ck4mBtzDpo-Lz", "type": "line", "x": 186.28236607142838, "y": 2090.6679687499995, "width": 18.15234375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 1533241170, "version": 42, "versionNonce": 35240910, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [18.15234375, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "xa9mzzxBh4iEAqzZ7K07I", "type": "line", "x": 185.73158482142838, "y": 2461.8828124999995, "width": 33.33984375, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 306917394, "version": 31, "versionNonce": 786196050, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [33.33984375, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "DqPOKqZKb17DNJrltyqEh", "type": "text", "x": 86.50892857142843, "y": 2080.3671874999995, "width": 169, "height": 19, "angle": 4.71238898038469, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 138502094, "version": 126, "versionNonce": 1283556814, "isDeleted": false, "boundElementIds": null, "text": "Mutually Exclusive", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15}, {"id": "yk-Qphl_g4lGpQEahRYfm", "type": "line", "x": 1762.041852678572, "y": 1342.693638392856, "width": 0, "height": 1296.3727678571427, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 652480914, "version": 166, "versionNonce": 1366388434, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [0, 1296.3727678571427]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "gfwjyHqAMTj32UXHQFJCR", "type": "line", "x": 1744.7594866071433, "y": 1342.386718749999, "width": 20.17857142857133, "height": 0, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 1270816590, "version": 10, "versionNonce": 30927762, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [20.17857142857133, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "WRATW6ooNJal8ubXcilJj", "type": "line", "x": 1746.0652901785718, "y": 2639.6300223214275, "width": 16.11049107142867, "height": 0, "angle": 0, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 412864142, "version": 26, "versionNonce": 1678081618, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [16.11049107142867, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "LBzxbhpT6EeU-V7dTG9AT", "type": "text", "x": 1730.3844866071431, "y": 1932.0630580357133, "width": 94, "height": 19, "angle": 1.5707963267948957, "strokeColor": "#862e9c", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 1742063566, "version": 74, "versionNonce": 1723557522, "isDeleted": false, "boundElementIds": null, "text": "Local View", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}}