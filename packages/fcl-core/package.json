{"name": "@onflow/fcl-core", "version": "1.19.0", "description": "Flow Client Library", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "jest": {"testEnvironment": "jsdom", "globals": {"PACKAGE_CURRENT_VERSION": "TESTVERSION"}}, "devDependencies": {"@onflow/fcl-bundle": "1.7.0", "@onflow/typedefs": "1.6.0", "@types/estree": "^1.0.6", "@types/jest": "^29.5.13", "@types/node": "^18.19.57", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "^4.9.5"}, "source": "src/fcl-core.ts", "main": "dist/fcl-core.js", "module": "dist/fcl-core.module.js", "unpkg": "dist/fcl-core.umd.min.js", "types": "types/fcl-core.d.ts", "scripts": {"pain": "npm publish --tag pain", "alpha": "npm publish --tag alpha", "prepublishOnly": "npm test && npm run build", "test": "jest", "test:watch": "jest --watch", "build": "npm run lint && fcl-bundle", "build:types": "tsc --project ./tsconfig-web.json && tsc --noResolve --project ./tsconfig-react-native.json", "start": "fcl-bundle --watch", "lint": "eslint ."}, "dependencies": {"@babel/runtime": "^7.25.7", "@improbable-eng/grpc-web": "^0.15.0", "@onflow/config": "1.5.2", "@onflow/interaction": "0.0.11", "@onflow/rlp": "1.2.3", "@onflow/sdk": "1.9.0", "@onflow/transport-http": "1.13.0", "@onflow/types": "1.4.1", "@onflow/util-actor": "1.3.4", "@onflow/util-address": "1.2.3", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3", "@onflow/util-semver": "1.0.3", "@onflow/util-template": "1.2.3", "@onflow/util-uid": "1.2.3", "abort-controller": "^3.0.0", "cross-fetch": "^4.0.0"}}