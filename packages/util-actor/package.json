{"name": "@onflow/util-actor", "version": "1.3.4", "description": "A mechanism for forcing order/transitions of scoped async state", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "devDependencies": {"@babel/preset-typescript": "^7.25.7", "@onflow/fcl-bundle": "1.7.0", "@types/jest": "^29.5.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0"}, "dependencies": {"@babel/runtime": "^7.25.7", "queue-microtask": "1.2.3"}, "source": "src/index.ts", "main": "dist/actor.js", "module": "dist/actor.module.js", "unpkg": "dist/actor.umd.js", "types": "types/index.d.ts", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch", "lint": "eslint src --ext .ts"}}