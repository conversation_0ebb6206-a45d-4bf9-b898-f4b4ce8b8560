{"env": {"browser": true, "es2021": true, "jest": true, "node": true}, "extends": ["plugin:jsdoc/recommended-typescript", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["jsdoc", "@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"jsdoc/require-jsdoc": 0, "jsdoc/tag-lines": 0, "jsdoc/require-param-description": 0, "jsdoc/require-param": 0, "jsdoc/valid-types": 0, "jsdoc/require-returns-description": 0, "jsdoc/require-returns": 0, "@typescript-eslint/no-explicit-any": "off"}, "ignorePatterns": ["**/dist/"]}