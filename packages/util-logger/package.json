{"name": "@onflow/util-logger", "version": "1.3.3", "description": "Logger for FCL-JS", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "devDependencies": {"@babel/preset-typescript": "^7.25.7", "@onflow/fcl-bundle": "1.7.0", "@types/jest": "^29.5.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0"}, "source": "src/util-logger.ts", "main": "dist/util-logger.js", "module": "dist/util-logger.module.js", "unpkg": "dist/util-logger.umd.js", "types": "types/util-logger.d.ts", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch"}, "dependencies": {"@babel/runtime": "^7.25.7"}, "peerDependencies": {"@onflow/util-config": ">1.1.1"}, "peerDependenciesMeta": {"@onflow/util-config": {"optional": true}}}