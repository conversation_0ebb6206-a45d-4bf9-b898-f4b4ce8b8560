{"name": "@onflow/transport-grpc", "version": "1.4.2-alpha.0", "description": "Flow SDK GRPC Transport Module", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "devDependencies": {"@onflow/fcl-bundle": "1.7.0", "@onflow/sdk": "1.9.0", "jest": "^29.7.0"}, "source": "src/sdk-send-grpc.js", "main": "dist/sdk-send-grpc.js", "module": "dist/sdk-send-grpc.module.js", "unpkg": "dist/sdk-send-grpc.umd.js", "scripts": {"alpha": "npm publish --tag alpha", "prepublishOnly": "npm test && npm run build", "test": "jest", "build": "fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch"}, "dependencies": {"@babel/runtime": "^7.25.7", "@improbable-eng/grpc-web": "^0.15.0", "@improbable-eng/grpc-web-node-http-transport": "^0.15.0", "@onflow/protobuf": "1.3.1", "@onflow/rlp": "1.2.3", "@onflow/util-address": "1.2.3", "@onflow/util-invariant": "1.2.4", "@onflow/util-template": "1.2.3"}}