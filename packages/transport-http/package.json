{"name": "@onflow/transport-http", "version": "1.13.0", "description": "Flow SDK HTTP Transport Module", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "devDependencies": {"@onflow/fcl-bundle": "1.7.0", "@onflow/rlp": "1.2.3", "@onflow/sdk": "1.9.0", "@onflow/types": "1.4.1", "jest": "^29.7.0", "jest-websocket-mock": "^2.5.0", "mock-socket": "^9.3.1"}, "source": "src/index.ts", "main": "dist/index.js", "module": "dist/index.module.js", "unpkg": "dist/index.umd.js", "types": "types/index.d.ts", "scripts": {"alpha": "npm publish --tag alpha", "prepublishOnly": "npm test && npm run build", "test": "jest", "build": "fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch"}, "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/util-address": "1.2.3", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3", "@onflow/util-template": "1.2.3", "abort-controller": "^3.0.0", "buffer": "^6.0.3", "cross-fetch": "^4.0.0", "events": "^3.3.0", "isomorphic-ws": "^5.0.0", "ws": "^8.18.0"}}