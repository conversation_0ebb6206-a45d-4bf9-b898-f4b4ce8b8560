export const CONTRACT_ADDRESSES = {
  testnet: {
    EVM: "0x8c5303eaa26202d6",
    FungibleToken: "0x9a0766d93b6608b7",
    NonFungibleToken: "0x631e88ae7f1d7c20",
    ViewResolver: "0x631e88ae7f1d7c20",
    MetadataViews: "0x631e88ae7f1d7c20",
    FlowToken: "0x7e60df042a9c0868",
    ScopedFTProviders: "0xdfc20aee650fcbdf",
    FlowEVMBridge: "0xdfc20aee650fcbdf",
    FlowEVMBridgeUtils: "0xdfc20aee650fcbdf",
    FlowEVMBridgeConfig: "0xdfc20aee650fcbdf",
    FungibleTokenMetadataViews: "0x9a0766d93b6608b7",
  },
  mainnet: {
    EVM: "0xe467b9dd11fa00df",
    FungibleToken: "0xf233dcee88fe0abe",
    NonFungibleToken: "0x1d7e57aa55817448",
    ViewResolver: "0x1d7e57aa55817448",
    MetadataViews: "0x1d7e57aa55817448",
    FlowToken: "0x1654653399040a61",
    ScopedFTProviders: "0x1e4aa0b87d10b141",
    FlowEVMBridge: "0x1e4aa0b87d10b141",
    FlowEVMBridgeUtils: "0x1e4aa0b87d10b141",
    FlowEVMBridgeConfig: "0x1e4aa0b87d10b141",
    FungibleTokenMetadataViews: "0xf233dcee88fe0abe",
  },
}

export const CADENCE_UFIX64_PRECISION = 8

export const DEFAULT_EVM_GAS_LIMIT = "15000000"
