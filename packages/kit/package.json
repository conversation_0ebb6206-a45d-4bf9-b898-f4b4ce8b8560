{"name": "@onflow/kit", "version": "0.4.0", "description": "React library for interacting with the Flow blockchain", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://www.flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "sideEffects": false, "main": "dist/index.js", "module": "dist/index.module.js", "types": "types/index.d.ts", "source": "src/index.ts", "unpkg": "dist/index.umd.js", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.25.7", "@onflow/fcl-bundle": "1.7.0", "@onflow/typedefs": "^1.6.0", "@testing-library/dom": "^10.4.0", "@types/jest": "^29.5.13", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}, "dependencies": {"@babel/runtime": "^7.25.7", "@tanstack/react-query": "^5.67.3", "@testing-library/react": "^16.2.0", "viem": "^2.29.2"}, "peerDependencies": {"@onflow/fcl": ">=1.18.0", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}}