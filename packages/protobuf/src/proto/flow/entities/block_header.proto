syntax = "proto3";

package flow.entities;

option go_package = "github.com/onflow/flow/protobuf/go/flow/entities";
option java_package = "org.onflow.protobuf.entities";

import "google/protobuf/timestamp.proto";

message BlockHeader {
  bytes id = 1;
  bytes parent_id = 2;
  uint64 height = 3;
  google.protobuf.Timestamp timestamp = 4;
  bytes payload_hash = 5;
  uint64 view = 6;
  repeated bytes parent_voter_ids = 7; // deprecated!! value will be empty. replaced by parent_vote_indices
  bytes parent_voter_sig_data = 8;
  bytes proposer_id = 9;
  bytes proposer_sig_data = 10;
  string chain_id = 11;
  bytes parent_voter_indices = 12;
  TimeoutCertificate last_view_tc = 13;
  uint64 parent_view = 14;
}

message TimeoutCertificate {
  uint64 view = 1;
  repeated uint64 high_qc_views = 2;
  QuorumCertificate highest_qc = 3;
  bytes signer_indices = 4;
  bytes sig_data = 5;
}

message QuorumCertificate {
  uint64 view = 1;
  bytes block_id = 2;
  bytes signer_indices = 3;
  bytes sig_data = 4;
}