syntax = "proto3";

package flow.entities;

option go_package = "github.com/onflow/flow/protobuf/go/flow/entities";
option java_package = "org.onflow.protobuf.entities";

message BlockSeal {
  bytes block_id = 1;
  bytes execution_receipt_id = 2;
  repeated bytes execution_receipt_signatures = 3;
  repeated bytes result_approval_signatures = 4;
  bytes final_state = 5;
  bytes result_id = 6;
  repeated AggregatedSignature aggregated_approval_sigs = 7;
}

message AggregatedSignature {
  repeated bytes verifier_signatures = 1;
  repeated bytes signer_ids = 2;
}