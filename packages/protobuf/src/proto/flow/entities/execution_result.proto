syntax = "proto3";

package flow.entities;

option go_package = "github.com/onflow/flow/protobuf/go/flow/entities";
option java_package = "org.onflow.protobuf.entities";

message ExecutionResult {
  bytes previous_result_id = 1;
  bytes block_id = 2;
  repeated Chunk chunks = 3;
  repeated ServiceEvent service_events = 4;
  bytes execution_data_id = 5 [deprecated = true];
}

message Chunk {
  uint32 CollectionIndex = 1;
  bytes start_state = 2;       // state when starting executing this chunk
  bytes event_collection = 3;  // Events generated by executing results
  bytes block_id = 4;  // Block id of the execution result this chunk belongs to
  uint64 total_computation_used =
      5;  // total amount of computation used by running all txs in this chunk
  uint32 number_of_transactions =
      6;                // number of transactions inside the collection
  uint64 index = 7;     // chunk index inside the ER (starts from zero)
  bytes end_state = 8;  // EndState inferred from next chunk or from the ER
  bytes execution_data_id = 9;
  bytes state_delta_commitment = 10; // a commitment over sorted list of register changes
}

message ServiceEvent {
  string type = 1;
  bytes payload = 2;
}

message ExecutionReceiptMeta {
  bytes executor_id = 1;
  bytes result_id = 2;
  repeated bytes spocks = 3;
  bytes executor_signature = 4;
}
