syntax = "proto3";

package flow.entities;

option go_package = "github.com/onflow/flow/protobuf/go/flow/entities";
option java_package = "org.onflow.protobuf.entities";

import "google/protobuf/timestamp.proto";

import "flow/entities/collection.proto";
import "flow/entities/block_seal.proto";
import "flow/entities/execution_result.proto";
import "flow/entities/block_header.proto";

enum BlockStatus {
  BLOCK_UNKNOWN = 0;
  BLOCK_FINALIZED = 1;
  BLOCK_SEALED = 2;
}

message Block {
  bytes id = 1;
  bytes parent_id = 2;
  uint64 height = 3;
  google.protobuf.Timestamp timestamp = 4;
  repeated CollectionGuarantee collection_guarantees = 5;
  repeated BlockSeal block_seals = 6;
  repeated bytes signatures = 7;
  repeated ExecutionReceiptMeta execution_receipt_metaList = 8;
  repeated ExecutionResult execution_result_list = 9;
  BlockHeader block_header = 10;
  bytes protocol_state_id = 11;
}
