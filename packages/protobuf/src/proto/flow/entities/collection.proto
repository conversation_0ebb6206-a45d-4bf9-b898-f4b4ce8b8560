syntax = "proto3";

package flow.entities;

option go_package = "github.com/onflow/flow/protobuf/go/flow/entities";
option java_package = "org.onflow.protobuf.entities";

message Collection {
  bytes id = 1;
  repeated bytes transaction_ids = 2;
}

message CollectionGuarantee {
  bytes collection_id = 1;
  repeated bytes signatures = 2;
  bytes reference_block_id = 3;
  bytes signature = 4;
  repeated bytes signer_ids = 5; // deprecated!! value will be empty. replaced by signer_indices
  bytes signer_indices = 6;
}
