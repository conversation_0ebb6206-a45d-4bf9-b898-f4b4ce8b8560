syntax = "proto3";

package flow.execution;

option go_package = "github.com/onflow/flow/protobuf/go/flow/execution";
option java_package = "org.onflow.protobuf.execution";

import "flow/entities/account.proto";
import "flow/entities/block_header.proto";
import "flow/entities/event.proto";
import "flow/entities/transaction.proto";

// ExecutionAPI is the API provided by the execution nodes.
service ExecutionAPI {
  // Ping is used to check if the access node is alive and healthy.
  rpc Ping(PingRequest) returns (PingResponse);

  // Accounts

  // GetAccountAtBlockID gets an account by address at the given block ID
  rpc GetAccountAtBlockID(GetAccountAtBlockIDRequest)
      returns (GetAccountAtBlockIDResponse);

  // Scripts

  // ExecuteScriptAtBlockID executes a ready-only Cadence script against the
  // execution state at the block with the given ID.
  rpc ExecuteScriptAtBlockID(ExecuteScriptAtBlockIDRequest)
      returns (ExecuteScriptAtBlockIDResponse);

  // Events

  // GetEventsForBlockIDs retrieves events for all the specified block IDs that
  // have the given type
  rpc GetEventsForBlockIDs(GetEventsForBlockIDsRequest)
      returns (GetEventsForBlockIDsResponse);

  // Transaction

  // GetTransactionResult gets the result of a transaction.
  rpc GetTransactionResult(GetTransactionResultRequest)
      returns (GetTransactionResultResponse);

  // GetTransactionResultByIndex gets the result of a transaction at the index.
  rpc GetTransactionResultByIndex(GetTransactionByIndexRequest)
      returns (GetTransactionResultResponse);

  // GetTransactionResultByIndex gets the results of all transactions in the
  // block ordered by transaction index.
  rpc GetTransactionResultsByBlockID(GetTransactionsByBlockIDRequest)
      returns (GetTransactionResultsResponse);

  // GetTransactionErrorMessage gets the error messages of a failed transaction by id.
  rpc GetTransactionErrorMessage(GetTransactionErrorMessageRequest)
    returns (GetTransactionErrorMessageResponse);

  // GetTransactionErrorMessageByIndex gets the error messages of a failed transaction at the index.
  rpc GetTransactionErrorMessageByIndex(GetTransactionErrorMessageByIndexRequest)
    returns (GetTransactionErrorMessageResponse);

  // GetTransactionErrorMessagesByBlockID gets the error messages of all failed transactions in the
  // block ordered by transaction index.
  rpc GetTransactionErrorMessagesByBlockID(GetTransactionErrorMessagesByBlockIDRequest)
    returns (GetTransactionErrorMessagesResponse);

  // Registers

  // GetRegisterAtBlockID collects a register at the block with the given ID (if
  // available).
  rpc GetRegisterAtBlockID(GetRegisterAtBlockIDRequest)
      returns (GetRegisterAtBlockIDResponse);

  // Block headers

  // GetLatestBlockHeader gets the latest sealed or unsealed block header.
  rpc GetLatestBlockHeader(GetLatestBlockHeaderRequest)
      returns (BlockHeaderResponse);
  // GetBlockHeaderByID gets a block header by ID.
  rpc GetBlockHeaderByID(GetBlockHeaderByIDRequest)
      returns (BlockHeaderResponse);
}

// Ping

message PingRequest {}

message PingResponse {}

// Accounts

message GetAccountAtBlockIDRequest {
  bytes block_id = 1;
  bytes address = 2;
}

message GetAccountAtBlockIDResponse {
  entities.Account account = 1;
}

// Scripts

message ExecuteScriptAtBlockIDRequest {
  bytes block_id = 1;
  bytes script = 2;
  repeated bytes arguments = 3;
}

message ExecuteScriptAtBlockIDResponse {
  bytes value = 1;
  uint64 computation_usage = 2;
}

// Events

message GetEventsForBlockIDsResponse {
  message Result {
    bytes block_id = 1;
    uint64 block_height = 2;
    repeated entities.Event events = 3;
  }
  repeated Result results = 1;
  entities.EventEncodingVersion event_encoding_version = 2;
}

message GetEventsForBlockIDsRequest {
  string type = 1;
  repeated bytes block_ids = 2;
}

// Transactions

message GetTransactionResultRequest {
  bytes block_id = 1;
  bytes transaction_id = 2;
}

message GetTransactionByIndexRequest {
  bytes block_id = 1;
  uint32 index = 2;
}

message GetTransactionResultResponse {
  uint32 status_code = 1;
  string error_message = 2;
  repeated entities.Event events = 3;
  entities.EventEncodingVersion event_encoding_version = 4;
  uint64 computation_usage = 5;
}

message GetTransactionsByBlockIDRequest {
  bytes block_id = 1;
}

message GetTransactionResultsResponse {
  repeated GetTransactionResultResponse transaction_results = 1;
  entities.EventEncodingVersion event_encoding_version = 2;
}

message GetTransactionErrorMessageRequest {
  bytes block_id = 1;
  bytes transaction_id = 2;
}

message GetTransactionErrorMessageByIndexRequest {
  bytes block_id = 1;
  uint32 index = 2;
}

message GetTransactionErrorMessageResponse {
  bytes transaction_id = 1;
  string error_message = 2;
}

message GetTransactionErrorMessagesByBlockIDRequest {
  bytes block_id = 1;
}

message GetTransactionErrorMessagesResponse {
  message Result {
    bytes transaction_id = 1;
    uint32 index = 2;
    string error_message = 3;
  }
  repeated Result results = 1;
}

// Registers

message GetRegisterAtBlockIDRequest {
  reserved 3;
  bytes block_id = 1;
  bytes register_owner = 2;
  // bytes register_controller = 3; @deprecated
  bytes register_key = 4;
}

message GetRegisterAtBlockIDResponse {
  bytes value = 1;
}

// Block Headers

message GetLatestBlockHeaderRequest {
  bool is_sealed = 1;
}

message GetBlockHeaderByIDRequest {
  bytes id = 1;
}

message BlockHeaderResponse {
  entities.BlockHeader block = 1;
}
