// package: flow.entities
// file: flow/entities/event.proto

import * as jspb from "google-protobuf";

export class Event extends jspb.Message {
  getType(): string;
  setType(value: string): void;

  getTransactionId(): Uint8Array | string;
  getTransactionId_asU8(): Uint8Array;
  getTransactionId_asB64(): string;
  setTransactionId(value: Uint8Array | string): void;

  getTransactionIndex(): number;
  setTransactionIndex(value: number): void;

  getEventIndex(): number;
  setEventIndex(value: number): void;

  getPayload(): Uint8Array | string;
  getPayload_asU8(): Uint8Array;
  getPayload_asB64(): string;
  setPayload(value: Uint8Array | string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Event.AsObject;
  static toObject(includeInstance: boolean, msg: Event): Event.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Event, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Event;
  static deserializeBinaryFromReader(message: Event, reader: jspb.BinaryReader): Event;
}

export namespace Event {
  export type AsObject = {
    type: string,
    transactionId: Uint8Array | string,
    transactionIndex: number,
    eventIndex: number,
    payload: Uint8Array | string,
  }
}

export interface EventEncodingVersionMap {
  JSON_CDC_V0: 0;
  CCF_V0: 1;
}

export const EventEncodingVersion: EventEncodingVersionMap;

