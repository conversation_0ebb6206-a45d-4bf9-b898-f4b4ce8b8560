// source: flow/entities/block_header.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js');
goog.object.extend(proto, google_protobuf_timestamp_pb);
goog.exportSymbol('proto.flow.entities.BlockHeader', null, global);
goog.exportSymbol('proto.flow.entities.QuorumCertificate', null, global);
goog.exportSymbol('proto.flow.entities.TimeoutCertificate', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.BlockHeader = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.BlockHeader.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.BlockHeader, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.BlockHeader.displayName = 'proto.flow.entities.BlockHeader';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.TimeoutCertificate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.TimeoutCertificate.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.TimeoutCertificate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.TimeoutCertificate.displayName = 'proto.flow.entities.TimeoutCertificate';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.QuorumCertificate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.flow.entities.QuorumCertificate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.QuorumCertificate.displayName = 'proto.flow.entities.QuorumCertificate';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.BlockHeader.repeatedFields_ = [7];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.BlockHeader.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.BlockHeader.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.BlockHeader} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.BlockHeader.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: msg.getId_asB64(),
    parentId: msg.getParentId_asB64(),
    height: jspb.Message.getFieldWithDefault(msg, 3, 0),
    timestamp: (f = msg.getTimestamp()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    payloadHash: msg.getPayloadHash_asB64(),
    view: jspb.Message.getFieldWithDefault(msg, 6, 0),
    parentVoterIdsList: msg.getParentVoterIdsList_asB64(),
    parentVoterSigData: msg.getParentVoterSigData_asB64(),
    proposerId: msg.getProposerId_asB64(),
    proposerSigData: msg.getProposerSigData_asB64(),
    chainId: jspb.Message.getFieldWithDefault(msg, 11, ""),
    parentVoterIndices: msg.getParentVoterIndices_asB64(),
    lastViewTc: (f = msg.getLastViewTc()) && proto.flow.entities.TimeoutCertificate.toObject(includeInstance, f),
    parentView: jspb.Message.getFieldWithDefault(msg, 14, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.BlockHeader}
 */
proto.flow.entities.BlockHeader.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.BlockHeader;
  return proto.flow.entities.BlockHeader.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.BlockHeader} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.BlockHeader}
 */
proto.flow.entities.BlockHeader.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setParentId(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setHeight(value);
      break;
    case 4:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setTimestamp(value);
      break;
    case 5:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setPayloadHash(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setView(value);
      break;
    case 7:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.addParentVoterIds(value);
      break;
    case 8:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setParentVoterSigData(value);
      break;
    case 9:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setProposerId(value);
      break;
    case 10:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setProposerSigData(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setChainId(value);
      break;
    case 12:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setParentVoterIndices(value);
      break;
    case 13:
      var value = new proto.flow.entities.TimeoutCertificate;
      reader.readMessage(value,proto.flow.entities.TimeoutCertificate.deserializeBinaryFromReader);
      msg.setLastViewTc(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setParentView(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockHeader.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.BlockHeader.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.BlockHeader} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.BlockHeader.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      1,
      f
    );
  }
  f = message.getParentId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = message.getHeight();
  if (f !== 0) {
    writer.writeUint64(
      3,
      f
    );
  }
  f = message.getTimestamp();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getPayloadHash_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      5,
      f
    );
  }
  f = message.getView();
  if (f !== 0) {
    writer.writeUint64(
      6,
      f
    );
  }
  f = message.getParentVoterIdsList_asU8();
  if (f.length > 0) {
    writer.writeRepeatedBytes(
      7,
      f
    );
  }
  f = message.getParentVoterSigData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      8,
      f
    );
  }
  f = message.getProposerId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      9,
      f
    );
  }
  f = message.getProposerSigData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      10,
      f
    );
  }
  f = message.getChainId();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getParentVoterIndices_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      12,
      f
    );
  }
  f = message.getLastViewTc();
  if (f != null) {
    writer.writeMessage(
      13,
      f,
      proto.flow.entities.TimeoutCertificate.serializeBinaryToWriter
    );
  }
  f = message.getParentView();
  if (f !== 0) {
    writer.writeUint64(
      14,
      f
    );
  }
};


/**
 * optional bytes id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.BlockHeader.prototype.getId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * optional bytes id = 1;
 * This is a type-conversion wrapper around `getId()`
 * @return {string}
 */
proto.flow.entities.BlockHeader.prototype.getId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getId()));
};


/**
 * optional bytes id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockHeader.prototype.getId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setId = function(value) {
  return jspb.Message.setProto3BytesField(this, 1, value);
};


/**
 * optional bytes parent_id = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.BlockHeader.prototype.getParentId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes parent_id = 2;
 * This is a type-conversion wrapper around `getParentId()`
 * @return {string}
 */
proto.flow.entities.BlockHeader.prototype.getParentId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getParentId()));
};


/**
 * optional bytes parent_id = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getParentId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockHeader.prototype.getParentId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getParentId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setParentId = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional uint64 height = 3;
 * @return {number}
 */
proto.flow.entities.BlockHeader.prototype.getHeight = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setHeight = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional google.protobuf.Timestamp timestamp = 4;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.flow.entities.BlockHeader.prototype.getTimestamp = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 4));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.flow.entities.BlockHeader} returns this
*/
proto.flow.entities.BlockHeader.prototype.setTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.clearTimestamp = function() {
  return this.setTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.entities.BlockHeader.prototype.hasTimestamp = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional bytes payload_hash = 5;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.BlockHeader.prototype.getPayloadHash = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * optional bytes payload_hash = 5;
 * This is a type-conversion wrapper around `getPayloadHash()`
 * @return {string}
 */
proto.flow.entities.BlockHeader.prototype.getPayloadHash_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getPayloadHash()));
};


/**
 * optional bytes payload_hash = 5;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getPayloadHash()`
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockHeader.prototype.getPayloadHash_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getPayloadHash()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setPayloadHash = function(value) {
  return jspb.Message.setProto3BytesField(this, 5, value);
};


/**
 * optional uint64 view = 6;
 * @return {number}
 */
proto.flow.entities.BlockHeader.prototype.getView = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setView = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * repeated bytes parent_voter_ids = 7;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.entities.BlockHeader.prototype.getParentVoterIdsList = function() {
  return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 7));
};


/**
 * repeated bytes parent_voter_ids = 7;
 * This is a type-conversion wrapper around `getParentVoterIdsList()`
 * @return {!Array<string>}
 */
proto.flow.entities.BlockHeader.prototype.getParentVoterIdsList_asB64 = function() {
  return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(
      this.getParentVoterIdsList()));
};


/**
 * repeated bytes parent_voter_ids = 7;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getParentVoterIdsList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.entities.BlockHeader.prototype.getParentVoterIdsList_asU8 = function() {
  return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(
      this.getParentVoterIdsList()));
};


/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setParentVoterIdsList = function(value) {
  return jspb.Message.setField(this, 7, value || []);
};


/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.addParentVoterIds = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 7, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.clearParentVoterIdsList = function() {
  return this.setParentVoterIdsList([]);
};


/**
 * optional bytes parent_voter_sig_data = 8;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.BlockHeader.prototype.getParentVoterSigData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * optional bytes parent_voter_sig_data = 8;
 * This is a type-conversion wrapper around `getParentVoterSigData()`
 * @return {string}
 */
proto.flow.entities.BlockHeader.prototype.getParentVoterSigData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getParentVoterSigData()));
};


/**
 * optional bytes parent_voter_sig_data = 8;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getParentVoterSigData()`
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockHeader.prototype.getParentVoterSigData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getParentVoterSigData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setParentVoterSigData = function(value) {
  return jspb.Message.setProto3BytesField(this, 8, value);
};


/**
 * optional bytes proposer_id = 9;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.BlockHeader.prototype.getProposerId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * optional bytes proposer_id = 9;
 * This is a type-conversion wrapper around `getProposerId()`
 * @return {string}
 */
proto.flow.entities.BlockHeader.prototype.getProposerId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getProposerId()));
};


/**
 * optional bytes proposer_id = 9;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getProposerId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockHeader.prototype.getProposerId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getProposerId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setProposerId = function(value) {
  return jspb.Message.setProto3BytesField(this, 9, value);
};


/**
 * optional bytes proposer_sig_data = 10;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.BlockHeader.prototype.getProposerSigData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * optional bytes proposer_sig_data = 10;
 * This is a type-conversion wrapper around `getProposerSigData()`
 * @return {string}
 */
proto.flow.entities.BlockHeader.prototype.getProposerSigData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getProposerSigData()));
};


/**
 * optional bytes proposer_sig_data = 10;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getProposerSigData()`
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockHeader.prototype.getProposerSigData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getProposerSigData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setProposerSigData = function(value) {
  return jspb.Message.setProto3BytesField(this, 10, value);
};


/**
 * optional string chain_id = 11;
 * @return {string}
 */
proto.flow.entities.BlockHeader.prototype.getChainId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setChainId = function(value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional bytes parent_voter_indices = 12;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.BlockHeader.prototype.getParentVoterIndices = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * optional bytes parent_voter_indices = 12;
 * This is a type-conversion wrapper around `getParentVoterIndices()`
 * @return {string}
 */
proto.flow.entities.BlockHeader.prototype.getParentVoterIndices_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getParentVoterIndices()));
};


/**
 * optional bytes parent_voter_indices = 12;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getParentVoterIndices()`
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockHeader.prototype.getParentVoterIndices_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getParentVoterIndices()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setParentVoterIndices = function(value) {
  return jspb.Message.setProto3BytesField(this, 12, value);
};


/**
 * optional TimeoutCertificate last_view_tc = 13;
 * @return {?proto.flow.entities.TimeoutCertificate}
 */
proto.flow.entities.BlockHeader.prototype.getLastViewTc = function() {
  return /** @type{?proto.flow.entities.TimeoutCertificate} */ (
    jspb.Message.getWrapperField(this, proto.flow.entities.TimeoutCertificate, 13));
};


/**
 * @param {?proto.flow.entities.TimeoutCertificate|undefined} value
 * @return {!proto.flow.entities.BlockHeader} returns this
*/
proto.flow.entities.BlockHeader.prototype.setLastViewTc = function(value) {
  return jspb.Message.setWrapperField(this, 13, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.clearLastViewTc = function() {
  return this.setLastViewTc(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.entities.BlockHeader.prototype.hasLastViewTc = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional uint64 parent_view = 14;
 * @return {number}
 */
proto.flow.entities.BlockHeader.prototype.getParentView = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/**
 * @param {number} value
 * @return {!proto.flow.entities.BlockHeader} returns this
 */
proto.flow.entities.BlockHeader.prototype.setParentView = function(value) {
  return jspb.Message.setProto3IntField(this, 14, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.TimeoutCertificate.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.TimeoutCertificate.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.TimeoutCertificate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.TimeoutCertificate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.TimeoutCertificate.toObject = function(includeInstance, msg) {
  var f, obj = {
    view: jspb.Message.getFieldWithDefault(msg, 1, 0),
    highQcViewsList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f,
    highestQc: (f = msg.getHighestQc()) && proto.flow.entities.QuorumCertificate.toObject(includeInstance, f),
    signerIndices: msg.getSignerIndices_asB64(),
    sigData: msg.getSigData_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.TimeoutCertificate}
 */
proto.flow.entities.TimeoutCertificate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.TimeoutCertificate;
  return proto.flow.entities.TimeoutCertificate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.TimeoutCertificate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.TimeoutCertificate}
 */
proto.flow.entities.TimeoutCertificate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setView(value);
      break;
    case 2:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedUint64() : [reader.readUint64()]);
      for (var i = 0; i < values.length; i++) {
        msg.addHighQcViews(values[i]);
      }
      break;
    case 3:
      var value = new proto.flow.entities.QuorumCertificate;
      reader.readMessage(value,proto.flow.entities.QuorumCertificate.deserializeBinaryFromReader);
      msg.setHighestQc(value);
      break;
    case 4:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setSignerIndices(value);
      break;
    case 5:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setSigData(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.TimeoutCertificate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.TimeoutCertificate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.TimeoutCertificate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.TimeoutCertificate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getView();
  if (f !== 0) {
    writer.writeUint64(
      1,
      f
    );
  }
  f = message.getHighQcViewsList();
  if (f.length > 0) {
    writer.writePackedUint64(
      2,
      f
    );
  }
  f = message.getHighestQc();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.flow.entities.QuorumCertificate.serializeBinaryToWriter
    );
  }
  f = message.getSignerIndices_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      4,
      f
    );
  }
  f = message.getSigData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      5,
      f
    );
  }
};


/**
 * optional uint64 view = 1;
 * @return {number}
 */
proto.flow.entities.TimeoutCertificate.prototype.getView = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.flow.entities.TimeoutCertificate} returns this
 */
proto.flow.entities.TimeoutCertificate.prototype.setView = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * repeated uint64 high_qc_views = 2;
 * @return {!Array<number>}
 */
proto.flow.entities.TimeoutCertificate.prototype.getHighQcViewsList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.flow.entities.TimeoutCertificate} returns this
 */
proto.flow.entities.TimeoutCertificate.prototype.setHighQcViewsList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.TimeoutCertificate} returns this
 */
proto.flow.entities.TimeoutCertificate.prototype.addHighQcViews = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.TimeoutCertificate} returns this
 */
proto.flow.entities.TimeoutCertificate.prototype.clearHighQcViewsList = function() {
  return this.setHighQcViewsList([]);
};


/**
 * optional QuorumCertificate highest_qc = 3;
 * @return {?proto.flow.entities.QuorumCertificate}
 */
proto.flow.entities.TimeoutCertificate.prototype.getHighestQc = function() {
  return /** @type{?proto.flow.entities.QuorumCertificate} */ (
    jspb.Message.getWrapperField(this, proto.flow.entities.QuorumCertificate, 3));
};


/**
 * @param {?proto.flow.entities.QuorumCertificate|undefined} value
 * @return {!proto.flow.entities.TimeoutCertificate} returns this
*/
proto.flow.entities.TimeoutCertificate.prototype.setHighestQc = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.entities.TimeoutCertificate} returns this
 */
proto.flow.entities.TimeoutCertificate.prototype.clearHighestQc = function() {
  return this.setHighestQc(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.entities.TimeoutCertificate.prototype.hasHighestQc = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional bytes signer_indices = 4;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.TimeoutCertificate.prototype.getSignerIndices = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * optional bytes signer_indices = 4;
 * This is a type-conversion wrapper around `getSignerIndices()`
 * @return {string}
 */
proto.flow.entities.TimeoutCertificate.prototype.getSignerIndices_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getSignerIndices()));
};


/**
 * optional bytes signer_indices = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSignerIndices()`
 * @return {!Uint8Array}
 */
proto.flow.entities.TimeoutCertificate.prototype.getSignerIndices_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getSignerIndices()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.TimeoutCertificate} returns this
 */
proto.flow.entities.TimeoutCertificate.prototype.setSignerIndices = function(value) {
  return jspb.Message.setProto3BytesField(this, 4, value);
};


/**
 * optional bytes sig_data = 5;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.TimeoutCertificate.prototype.getSigData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * optional bytes sig_data = 5;
 * This is a type-conversion wrapper around `getSigData()`
 * @return {string}
 */
proto.flow.entities.TimeoutCertificate.prototype.getSigData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getSigData()));
};


/**
 * optional bytes sig_data = 5;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSigData()`
 * @return {!Uint8Array}
 */
proto.flow.entities.TimeoutCertificate.prototype.getSigData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getSigData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.TimeoutCertificate} returns this
 */
proto.flow.entities.TimeoutCertificate.prototype.setSigData = function(value) {
  return jspb.Message.setProto3BytesField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.QuorumCertificate.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.QuorumCertificate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.QuorumCertificate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.QuorumCertificate.toObject = function(includeInstance, msg) {
  var f, obj = {
    view: jspb.Message.getFieldWithDefault(msg, 1, 0),
    blockId: msg.getBlockId_asB64(),
    signerIndices: msg.getSignerIndices_asB64(),
    sigData: msg.getSigData_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.QuorumCertificate}
 */
proto.flow.entities.QuorumCertificate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.QuorumCertificate;
  return proto.flow.entities.QuorumCertificate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.QuorumCertificate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.QuorumCertificate}
 */
proto.flow.entities.QuorumCertificate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setView(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setBlockId(value);
      break;
    case 3:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setSignerIndices(value);
      break;
    case 4:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setSigData(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.QuorumCertificate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.QuorumCertificate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.QuorumCertificate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.QuorumCertificate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getView();
  if (f !== 0) {
    writer.writeUint64(
      1,
      f
    );
  }
  f = message.getBlockId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = message.getSignerIndices_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      3,
      f
    );
  }
  f = message.getSigData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      4,
      f
    );
  }
};


/**
 * optional uint64 view = 1;
 * @return {number}
 */
proto.flow.entities.QuorumCertificate.prototype.getView = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.flow.entities.QuorumCertificate} returns this
 */
proto.flow.entities.QuorumCertificate.prototype.setView = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional bytes block_id = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.QuorumCertificate.prototype.getBlockId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes block_id = 2;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.entities.QuorumCertificate.prototype.getBlockId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getBlockId()));
};


/**
 * optional bytes block_id = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.QuorumCertificate.prototype.getBlockId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getBlockId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.QuorumCertificate} returns this
 */
proto.flow.entities.QuorumCertificate.prototype.setBlockId = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional bytes signer_indices = 3;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.QuorumCertificate.prototype.getSignerIndices = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * optional bytes signer_indices = 3;
 * This is a type-conversion wrapper around `getSignerIndices()`
 * @return {string}
 */
proto.flow.entities.QuorumCertificate.prototype.getSignerIndices_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getSignerIndices()));
};


/**
 * optional bytes signer_indices = 3;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSignerIndices()`
 * @return {!Uint8Array}
 */
proto.flow.entities.QuorumCertificate.prototype.getSignerIndices_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getSignerIndices()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.QuorumCertificate} returns this
 */
proto.flow.entities.QuorumCertificate.prototype.setSignerIndices = function(value) {
  return jspb.Message.setProto3BytesField(this, 3, value);
};


/**
 * optional bytes sig_data = 4;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.QuorumCertificate.prototype.getSigData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * optional bytes sig_data = 4;
 * This is a type-conversion wrapper around `getSigData()`
 * @return {string}
 */
proto.flow.entities.QuorumCertificate.prototype.getSigData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getSigData()));
};


/**
 * optional bytes sig_data = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSigData()`
 * @return {!Uint8Array}
 */
proto.flow.entities.QuorumCertificate.prototype.getSigData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getSigData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.QuorumCertificate} returns this
 */
proto.flow.entities.QuorumCertificate.prototype.setSigData = function(value) {
  return jspb.Message.setProto3BytesField(this, 4, value);
};


goog.object.extend(exports, proto.flow.entities);
