// source: flow/entities/collection.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

goog.exportSymbol('proto.flow.entities.Collection', null, global);
goog.exportSymbol('proto.flow.entities.CollectionGuarantee', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.Collection = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.Collection.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.Collection, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.Collection.displayName = 'proto.flow.entities.Collection';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.CollectionGuarantee = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.CollectionGuarantee.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.CollectionGuarantee, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.CollectionGuarantee.displayName = 'proto.flow.entities.CollectionGuarantee';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.Collection.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.Collection.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.Collection.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.Collection} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.Collection.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: msg.getId_asB64(),
    transactionIdsList: msg.getTransactionIdsList_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.Collection}
 */
proto.flow.entities.Collection.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.Collection;
  return proto.flow.entities.Collection.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.Collection} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.Collection}
 */
proto.flow.entities.Collection.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.addTransactionIds(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.Collection.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.Collection.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.Collection} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.Collection.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      1,
      f
    );
  }
  f = message.getTransactionIdsList_asU8();
  if (f.length > 0) {
    writer.writeRepeatedBytes(
      2,
      f
    );
  }
};


/**
 * optional bytes id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Collection.prototype.getId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * optional bytes id = 1;
 * This is a type-conversion wrapper around `getId()`
 * @return {string}
 */
proto.flow.entities.Collection.prototype.getId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getId()));
};


/**
 * optional bytes id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Collection.prototype.getId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Collection} returns this
 */
proto.flow.entities.Collection.prototype.setId = function(value) {
  return jspb.Message.setProto3BytesField(this, 1, value);
};


/**
 * repeated bytes transaction_ids = 2;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.entities.Collection.prototype.getTransactionIdsList = function() {
  return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * repeated bytes transaction_ids = 2;
 * This is a type-conversion wrapper around `getTransactionIdsList()`
 * @return {!Array<string>}
 */
proto.flow.entities.Collection.prototype.getTransactionIdsList_asB64 = function() {
  return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(
      this.getTransactionIdsList()));
};


/**
 * repeated bytes transaction_ids = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getTransactionIdsList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.entities.Collection.prototype.getTransactionIdsList_asU8 = function() {
  return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(
      this.getTransactionIdsList()));
};


/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.entities.Collection} returns this
 */
proto.flow.entities.Collection.prototype.setTransactionIdsList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.Collection} returns this
 */
proto.flow.entities.Collection.prototype.addTransactionIds = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.Collection} returns this
 */
proto.flow.entities.Collection.prototype.clearTransactionIdsList = function() {
  return this.setTransactionIdsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.CollectionGuarantee.repeatedFields_ = [2,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.CollectionGuarantee.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.CollectionGuarantee.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.CollectionGuarantee} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.CollectionGuarantee.toObject = function(includeInstance, msg) {
  var f, obj = {
    collectionId: msg.getCollectionId_asB64(),
    signaturesList: msg.getSignaturesList_asB64(),
    referenceBlockId: msg.getReferenceBlockId_asB64(),
    signature: msg.getSignature_asB64(),
    signerIdsList: msg.getSignerIdsList_asB64(),
    signerIndices: msg.getSignerIndices_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.CollectionGuarantee}
 */
proto.flow.entities.CollectionGuarantee.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.CollectionGuarantee;
  return proto.flow.entities.CollectionGuarantee.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.CollectionGuarantee} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.CollectionGuarantee}
 */
proto.flow.entities.CollectionGuarantee.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setCollectionId(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.addSignatures(value);
      break;
    case 3:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setReferenceBlockId(value);
      break;
    case 4:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setSignature(value);
      break;
    case 5:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.addSignerIds(value);
      break;
    case 6:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setSignerIndices(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.CollectionGuarantee.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.CollectionGuarantee.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.CollectionGuarantee} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.CollectionGuarantee.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCollectionId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      1,
      f
    );
  }
  f = message.getSignaturesList_asU8();
  if (f.length > 0) {
    writer.writeRepeatedBytes(
      2,
      f
    );
  }
  f = message.getReferenceBlockId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      3,
      f
    );
  }
  f = message.getSignature_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      4,
      f
    );
  }
  f = message.getSignerIdsList_asU8();
  if (f.length > 0) {
    writer.writeRepeatedBytes(
      5,
      f
    );
  }
  f = message.getSignerIndices_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      6,
      f
    );
  }
};


/**
 * optional bytes collection_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.CollectionGuarantee.prototype.getCollectionId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * optional bytes collection_id = 1;
 * This is a type-conversion wrapper around `getCollectionId()`
 * @return {string}
 */
proto.flow.entities.CollectionGuarantee.prototype.getCollectionId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getCollectionId()));
};


/**
 * optional bytes collection_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getCollectionId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.CollectionGuarantee.prototype.getCollectionId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getCollectionId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.setCollectionId = function(value) {
  return jspb.Message.setProto3BytesField(this, 1, value);
};


/**
 * repeated bytes signatures = 2;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignaturesList = function() {
  return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * repeated bytes signatures = 2;
 * This is a type-conversion wrapper around `getSignaturesList()`
 * @return {!Array<string>}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignaturesList_asB64 = function() {
  return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(
      this.getSignaturesList()));
};


/**
 * repeated bytes signatures = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSignaturesList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignaturesList_asU8 = function() {
  return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(
      this.getSignaturesList()));
};


/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.setSignaturesList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.addSignatures = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.clearSignaturesList = function() {
  return this.setSignaturesList([]);
};


/**
 * optional bytes reference_block_id = 3;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.CollectionGuarantee.prototype.getReferenceBlockId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * optional bytes reference_block_id = 3;
 * This is a type-conversion wrapper around `getReferenceBlockId()`
 * @return {string}
 */
proto.flow.entities.CollectionGuarantee.prototype.getReferenceBlockId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getReferenceBlockId()));
};


/**
 * optional bytes reference_block_id = 3;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getReferenceBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.CollectionGuarantee.prototype.getReferenceBlockId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getReferenceBlockId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.setReferenceBlockId = function(value) {
  return jspb.Message.setProto3BytesField(this, 3, value);
};


/**
 * optional bytes signature = 4;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignature = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * optional bytes signature = 4;
 * This is a type-conversion wrapper around `getSignature()`
 * @return {string}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignature_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getSignature()));
};


/**
 * optional bytes signature = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSignature()`
 * @return {!Uint8Array}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignature_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getSignature()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.setSignature = function(value) {
  return jspb.Message.setProto3BytesField(this, 4, value);
};


/**
 * repeated bytes signer_ids = 5;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignerIdsList = function() {
  return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 5));
};


/**
 * repeated bytes signer_ids = 5;
 * This is a type-conversion wrapper around `getSignerIdsList()`
 * @return {!Array<string>}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignerIdsList_asB64 = function() {
  return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(
      this.getSignerIdsList()));
};


/**
 * repeated bytes signer_ids = 5;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSignerIdsList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignerIdsList_asU8 = function() {
  return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(
      this.getSignerIdsList()));
};


/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.setSignerIdsList = function(value) {
  return jspb.Message.setField(this, 5, value || []);
};


/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.addSignerIds = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 5, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.clearSignerIdsList = function() {
  return this.setSignerIdsList([]);
};


/**
 * optional bytes signer_indices = 6;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignerIndices = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * optional bytes signer_indices = 6;
 * This is a type-conversion wrapper around `getSignerIndices()`
 * @return {string}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignerIndices_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getSignerIndices()));
};


/**
 * optional bytes signer_indices = 6;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSignerIndices()`
 * @return {!Uint8Array}
 */
proto.flow.entities.CollectionGuarantee.prototype.getSignerIndices_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getSignerIndices()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.CollectionGuarantee} returns this
 */
proto.flow.entities.CollectionGuarantee.prototype.setSignerIndices = function(value) {
  return jspb.Message.setProto3BytesField(this, 6, value);
};


goog.object.extend(exports, proto.flow.entities);
