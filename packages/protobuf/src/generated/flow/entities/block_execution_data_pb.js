// source: flow/entities/block_execution_data.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var flow_entities_event_pb = require('../../flow/entities/event_pb.js');
goog.object.extend(proto, flow_entities_event_pb);
var flow_entities_transaction_pb = require('../../flow/entities/transaction_pb.js');
goog.object.extend(proto, flow_entities_transaction_pb);
goog.exportSymbol('proto.flow.entities.BlockExecutionData', null, global);
goog.exportSymbol('proto.flow.entities.ChunkExecutionData', null, global);
goog.exportSymbol('proto.flow.entities.ExecutionDataCollection', null, global);
goog.exportSymbol('proto.flow.entities.ExecutionDataTransactionResult', null, global);
goog.exportSymbol('proto.flow.entities.KeyPart', null, global);
goog.exportSymbol('proto.flow.entities.Payload', null, global);
goog.exportSymbol('proto.flow.entities.TrieUpdate', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.BlockExecutionData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.BlockExecutionData.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.BlockExecutionData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.BlockExecutionData.displayName = 'proto.flow.entities.BlockExecutionData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.ChunkExecutionData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.ChunkExecutionData.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.ChunkExecutionData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.ChunkExecutionData.displayName = 'proto.flow.entities.ChunkExecutionData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.ExecutionDataCollection = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.ExecutionDataCollection.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.ExecutionDataCollection, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.ExecutionDataCollection.displayName = 'proto.flow.entities.ExecutionDataCollection';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.TrieUpdate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.TrieUpdate.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.TrieUpdate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.TrieUpdate.displayName = 'proto.flow.entities.TrieUpdate';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.Payload = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.Payload.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.Payload, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.Payload.displayName = 'proto.flow.entities.Payload';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.KeyPart = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.flow.entities.KeyPart, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.KeyPart.displayName = 'proto.flow.entities.KeyPart';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.ExecutionDataTransactionResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.flow.entities.ExecutionDataTransactionResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.flow.entities.ExecutionDataTransactionResult.displayName = 'proto.flow.entities.ExecutionDataTransactionResult';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.BlockExecutionData.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.BlockExecutionData.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.BlockExecutionData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.BlockExecutionData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.BlockExecutionData.toObject = function(includeInstance, msg) {
  var f, obj = {
    blockId: msg.getBlockId_asB64(),
    chunkExecutionDataList: jspb.Message.toObjectList(msg.getChunkExecutionDataList(),
    proto.flow.entities.ChunkExecutionData.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.BlockExecutionData}
 */
proto.flow.entities.BlockExecutionData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.BlockExecutionData;
  return proto.flow.entities.BlockExecutionData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.BlockExecutionData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.BlockExecutionData}
 */
proto.flow.entities.BlockExecutionData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setBlockId(value);
      break;
    case 2:
      var value = new proto.flow.entities.ChunkExecutionData;
      reader.readMessage(value,proto.flow.entities.ChunkExecutionData.deserializeBinaryFromReader);
      msg.addChunkExecutionData(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockExecutionData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.BlockExecutionData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.BlockExecutionData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.BlockExecutionData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getBlockId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      1,
      f
    );
  }
  f = message.getChunkExecutionDataList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.flow.entities.ChunkExecutionData.serializeBinaryToWriter
    );
  }
};


/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.BlockExecutionData.prototype.getBlockId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.entities.BlockExecutionData.prototype.getBlockId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getBlockId()));
};


/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.BlockExecutionData.prototype.getBlockId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getBlockId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.BlockExecutionData} returns this
 */
proto.flow.entities.BlockExecutionData.prototype.setBlockId = function(value) {
  return jspb.Message.setProto3BytesField(this, 1, value);
};


/**
 * repeated ChunkExecutionData chunk_execution_data = 2;
 * @return {!Array<!proto.flow.entities.ChunkExecutionData>}
 */
proto.flow.entities.BlockExecutionData.prototype.getChunkExecutionDataList = function() {
  return /** @type{!Array<!proto.flow.entities.ChunkExecutionData>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.ChunkExecutionData, 2));
};


/**
 * @param {!Array<!proto.flow.entities.ChunkExecutionData>} value
 * @return {!proto.flow.entities.BlockExecutionData} returns this
*/
proto.flow.entities.BlockExecutionData.prototype.setChunkExecutionDataList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.flow.entities.ChunkExecutionData=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.ChunkExecutionData}
 */
proto.flow.entities.BlockExecutionData.prototype.addChunkExecutionData = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.flow.entities.ChunkExecutionData, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.BlockExecutionData} returns this
 */
proto.flow.entities.BlockExecutionData.prototype.clearChunkExecutionDataList = function() {
  return this.setChunkExecutionDataList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.ChunkExecutionData.repeatedFields_ = [2,4];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.ChunkExecutionData.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.ChunkExecutionData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.ChunkExecutionData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.ChunkExecutionData.toObject = function(includeInstance, msg) {
  var f, obj = {
    collection: (f = msg.getCollection()) && proto.flow.entities.ExecutionDataCollection.toObject(includeInstance, f),
    eventsList: jspb.Message.toObjectList(msg.getEventsList(),
    flow_entities_event_pb.Event.toObject, includeInstance),
    trieupdate: (f = msg.getTrieupdate()) && proto.flow.entities.TrieUpdate.toObject(includeInstance, f),
    transactionResultsList: jspb.Message.toObjectList(msg.getTransactionResultsList(),
    proto.flow.entities.ExecutionDataTransactionResult.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.ChunkExecutionData}
 */
proto.flow.entities.ChunkExecutionData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.ChunkExecutionData;
  return proto.flow.entities.ChunkExecutionData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.ChunkExecutionData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.ChunkExecutionData}
 */
proto.flow.entities.ChunkExecutionData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.flow.entities.ExecutionDataCollection;
      reader.readMessage(value,proto.flow.entities.ExecutionDataCollection.deserializeBinaryFromReader);
      msg.setCollection(value);
      break;
    case 2:
      var value = new flow_entities_event_pb.Event;
      reader.readMessage(value,flow_entities_event_pb.Event.deserializeBinaryFromReader);
      msg.addEvents(value);
      break;
    case 3:
      var value = new proto.flow.entities.TrieUpdate;
      reader.readMessage(value,proto.flow.entities.TrieUpdate.deserializeBinaryFromReader);
      msg.setTrieupdate(value);
      break;
    case 4:
      var value = new proto.flow.entities.ExecutionDataTransactionResult;
      reader.readMessage(value,proto.flow.entities.ExecutionDataTransactionResult.deserializeBinaryFromReader);
      msg.addTransactionResults(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.ChunkExecutionData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.ChunkExecutionData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.ChunkExecutionData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.ChunkExecutionData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCollection();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.flow.entities.ExecutionDataCollection.serializeBinaryToWriter
    );
  }
  f = message.getEventsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      flow_entities_event_pb.Event.serializeBinaryToWriter
    );
  }
  f = message.getTrieupdate();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.flow.entities.TrieUpdate.serializeBinaryToWriter
    );
  }
  f = message.getTransactionResultsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.flow.entities.ExecutionDataTransactionResult.serializeBinaryToWriter
    );
  }
};


/**
 * optional ExecutionDataCollection collection = 1;
 * @return {?proto.flow.entities.ExecutionDataCollection}
 */
proto.flow.entities.ChunkExecutionData.prototype.getCollection = function() {
  return /** @type{?proto.flow.entities.ExecutionDataCollection} */ (
    jspb.Message.getWrapperField(this, proto.flow.entities.ExecutionDataCollection, 1));
};


/**
 * @param {?proto.flow.entities.ExecutionDataCollection|undefined} value
 * @return {!proto.flow.entities.ChunkExecutionData} returns this
*/
proto.flow.entities.ChunkExecutionData.prototype.setCollection = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.entities.ChunkExecutionData} returns this
 */
proto.flow.entities.ChunkExecutionData.prototype.clearCollection = function() {
  return this.setCollection(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.entities.ChunkExecutionData.prototype.hasCollection = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * repeated Event events = 2;
 * @return {!Array<!proto.flow.entities.Event>}
 */
proto.flow.entities.ChunkExecutionData.prototype.getEventsList = function() {
  return /** @type{!Array<!proto.flow.entities.Event>} */ (
    jspb.Message.getRepeatedWrapperField(this, flow_entities_event_pb.Event, 2));
};


/**
 * @param {!Array<!proto.flow.entities.Event>} value
 * @return {!proto.flow.entities.ChunkExecutionData} returns this
*/
proto.flow.entities.ChunkExecutionData.prototype.setEventsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.flow.entities.Event=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.Event}
 */
proto.flow.entities.ChunkExecutionData.prototype.addEvents = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.flow.entities.Event, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.ChunkExecutionData} returns this
 */
proto.flow.entities.ChunkExecutionData.prototype.clearEventsList = function() {
  return this.setEventsList([]);
};


/**
 * optional TrieUpdate trieUpdate = 3;
 * @return {?proto.flow.entities.TrieUpdate}
 */
proto.flow.entities.ChunkExecutionData.prototype.getTrieupdate = function() {
  return /** @type{?proto.flow.entities.TrieUpdate} */ (
    jspb.Message.getWrapperField(this, proto.flow.entities.TrieUpdate, 3));
};


/**
 * @param {?proto.flow.entities.TrieUpdate|undefined} value
 * @return {!proto.flow.entities.ChunkExecutionData} returns this
*/
proto.flow.entities.ChunkExecutionData.prototype.setTrieupdate = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.entities.ChunkExecutionData} returns this
 */
proto.flow.entities.ChunkExecutionData.prototype.clearTrieupdate = function() {
  return this.setTrieupdate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.entities.ChunkExecutionData.prototype.hasTrieupdate = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * repeated ExecutionDataTransactionResult transaction_results = 4;
 * @return {!Array<!proto.flow.entities.ExecutionDataTransactionResult>}
 */
proto.flow.entities.ChunkExecutionData.prototype.getTransactionResultsList = function() {
  return /** @type{!Array<!proto.flow.entities.ExecutionDataTransactionResult>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.ExecutionDataTransactionResult, 4));
};


/**
 * @param {!Array<!proto.flow.entities.ExecutionDataTransactionResult>} value
 * @return {!proto.flow.entities.ChunkExecutionData} returns this
*/
proto.flow.entities.ChunkExecutionData.prototype.setTransactionResultsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.flow.entities.ExecutionDataTransactionResult=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.ExecutionDataTransactionResult}
 */
proto.flow.entities.ChunkExecutionData.prototype.addTransactionResults = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.flow.entities.ExecutionDataTransactionResult, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.ChunkExecutionData} returns this
 */
proto.flow.entities.ChunkExecutionData.prototype.clearTransactionResultsList = function() {
  return this.setTransactionResultsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.ExecutionDataCollection.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.ExecutionDataCollection.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.ExecutionDataCollection.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.ExecutionDataCollection} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.ExecutionDataCollection.toObject = function(includeInstance, msg) {
  var f, obj = {
    transactionsList: jspb.Message.toObjectList(msg.getTransactionsList(),
    flow_entities_transaction_pb.Transaction.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.ExecutionDataCollection}
 */
proto.flow.entities.ExecutionDataCollection.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.ExecutionDataCollection;
  return proto.flow.entities.ExecutionDataCollection.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.ExecutionDataCollection} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.ExecutionDataCollection}
 */
proto.flow.entities.ExecutionDataCollection.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new flow_entities_transaction_pb.Transaction;
      reader.readMessage(value,flow_entities_transaction_pb.Transaction.deserializeBinaryFromReader);
      msg.addTransactions(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionDataCollection.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.ExecutionDataCollection.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.ExecutionDataCollection} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.ExecutionDataCollection.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTransactionsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      flow_entities_transaction_pb.Transaction.serializeBinaryToWriter
    );
  }
};


/**
 * repeated Transaction transactions = 1;
 * @return {!Array<!proto.flow.entities.Transaction>}
 */
proto.flow.entities.ExecutionDataCollection.prototype.getTransactionsList = function() {
  return /** @type{!Array<!proto.flow.entities.Transaction>} */ (
    jspb.Message.getRepeatedWrapperField(this, flow_entities_transaction_pb.Transaction, 1));
};


/**
 * @param {!Array<!proto.flow.entities.Transaction>} value
 * @return {!proto.flow.entities.ExecutionDataCollection} returns this
*/
proto.flow.entities.ExecutionDataCollection.prototype.setTransactionsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.flow.entities.Transaction=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.Transaction}
 */
proto.flow.entities.ExecutionDataCollection.prototype.addTransactions = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.flow.entities.Transaction, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.ExecutionDataCollection} returns this
 */
proto.flow.entities.ExecutionDataCollection.prototype.clearTransactionsList = function() {
  return this.setTransactionsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.TrieUpdate.repeatedFields_ = [2,3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.TrieUpdate.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.TrieUpdate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.TrieUpdate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.TrieUpdate.toObject = function(includeInstance, msg) {
  var f, obj = {
    rootHash: msg.getRootHash_asB64(),
    pathsList: msg.getPathsList_asB64(),
    payloadsList: jspb.Message.toObjectList(msg.getPayloadsList(),
    proto.flow.entities.Payload.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.TrieUpdate}
 */
proto.flow.entities.TrieUpdate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.TrieUpdate;
  return proto.flow.entities.TrieUpdate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.TrieUpdate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.TrieUpdate}
 */
proto.flow.entities.TrieUpdate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setRootHash(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.addPaths(value);
      break;
    case 3:
      var value = new proto.flow.entities.Payload;
      reader.readMessage(value,proto.flow.entities.Payload.deserializeBinaryFromReader);
      msg.addPayloads(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.TrieUpdate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.TrieUpdate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.TrieUpdate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.TrieUpdate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRootHash_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      1,
      f
    );
  }
  f = message.getPathsList_asU8();
  if (f.length > 0) {
    writer.writeRepeatedBytes(
      2,
      f
    );
  }
  f = message.getPayloadsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.flow.entities.Payload.serializeBinaryToWriter
    );
  }
};


/**
 * optional bytes root_hash = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.TrieUpdate.prototype.getRootHash = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * optional bytes root_hash = 1;
 * This is a type-conversion wrapper around `getRootHash()`
 * @return {string}
 */
proto.flow.entities.TrieUpdate.prototype.getRootHash_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getRootHash()));
};


/**
 * optional bytes root_hash = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getRootHash()`
 * @return {!Uint8Array}
 */
proto.flow.entities.TrieUpdate.prototype.getRootHash_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getRootHash()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.TrieUpdate} returns this
 */
proto.flow.entities.TrieUpdate.prototype.setRootHash = function(value) {
  return jspb.Message.setProto3BytesField(this, 1, value);
};


/**
 * repeated bytes paths = 2;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.entities.TrieUpdate.prototype.getPathsList = function() {
  return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * repeated bytes paths = 2;
 * This is a type-conversion wrapper around `getPathsList()`
 * @return {!Array<string>}
 */
proto.flow.entities.TrieUpdate.prototype.getPathsList_asB64 = function() {
  return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(
      this.getPathsList()));
};


/**
 * repeated bytes paths = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getPathsList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.entities.TrieUpdate.prototype.getPathsList_asU8 = function() {
  return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(
      this.getPathsList()));
};


/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.entities.TrieUpdate} returns this
 */
proto.flow.entities.TrieUpdate.prototype.setPathsList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.TrieUpdate} returns this
 */
proto.flow.entities.TrieUpdate.prototype.addPaths = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.TrieUpdate} returns this
 */
proto.flow.entities.TrieUpdate.prototype.clearPathsList = function() {
  return this.setPathsList([]);
};


/**
 * repeated Payload payloads = 3;
 * @return {!Array<!proto.flow.entities.Payload>}
 */
proto.flow.entities.TrieUpdate.prototype.getPayloadsList = function() {
  return /** @type{!Array<!proto.flow.entities.Payload>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.Payload, 3));
};


/**
 * @param {!Array<!proto.flow.entities.Payload>} value
 * @return {!proto.flow.entities.TrieUpdate} returns this
*/
proto.flow.entities.TrieUpdate.prototype.setPayloadsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.flow.entities.Payload=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.Payload}
 */
proto.flow.entities.TrieUpdate.prototype.addPayloads = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.flow.entities.Payload, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.TrieUpdate} returns this
 */
proto.flow.entities.TrieUpdate.prototype.clearPayloadsList = function() {
  return this.setPayloadsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.Payload.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.Payload.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.Payload.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.Payload} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.Payload.toObject = function(includeInstance, msg) {
  var f, obj = {
    keypartList: jspb.Message.toObjectList(msg.getKeypartList(),
    proto.flow.entities.KeyPart.toObject, includeInstance),
    value: msg.getValue_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.Payload}
 */
proto.flow.entities.Payload.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.Payload;
  return proto.flow.entities.Payload.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.Payload} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.Payload}
 */
proto.flow.entities.Payload.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.flow.entities.KeyPart;
      reader.readMessage(value,proto.flow.entities.KeyPart.deserializeBinaryFromReader);
      msg.addKeypart(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.Payload.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.Payload.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.Payload} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.Payload.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getKeypartList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.flow.entities.KeyPart.serializeBinaryToWriter
    );
  }
  f = message.getValue_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
};


/**
 * repeated KeyPart keyPart = 1;
 * @return {!Array<!proto.flow.entities.KeyPart>}
 */
proto.flow.entities.Payload.prototype.getKeypartList = function() {
  return /** @type{!Array<!proto.flow.entities.KeyPart>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.KeyPart, 1));
};


/**
 * @param {!Array<!proto.flow.entities.KeyPart>} value
 * @return {!proto.flow.entities.Payload} returns this
*/
proto.flow.entities.Payload.prototype.setKeypartList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.flow.entities.KeyPart=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.KeyPart}
 */
proto.flow.entities.Payload.prototype.addKeypart = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.flow.entities.KeyPart, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.Payload} returns this
 */
proto.flow.entities.Payload.prototype.clearKeypartList = function() {
  return this.setKeypartList([]);
};


/**
 * optional bytes value = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Payload.prototype.getValue = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes value = 2;
 * This is a type-conversion wrapper around `getValue()`
 * @return {string}
 */
proto.flow.entities.Payload.prototype.getValue_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getValue()));
};


/**
 * optional bytes value = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getValue()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Payload.prototype.getValue_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getValue()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Payload} returns this
 */
proto.flow.entities.Payload.prototype.setValue = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.KeyPart.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.KeyPart.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.KeyPart} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.KeyPart.toObject = function(includeInstance, msg) {
  var f, obj = {
    type: jspb.Message.getFieldWithDefault(msg, 1, 0),
    value: msg.getValue_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.KeyPart}
 */
proto.flow.entities.KeyPart.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.KeyPart;
  return proto.flow.entities.KeyPart.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.KeyPart} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.KeyPart}
 */
proto.flow.entities.KeyPart.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setType(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.KeyPart.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.KeyPart.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.KeyPart} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.KeyPart.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getType();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getValue_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
};


/**
 * optional uint32 type = 1;
 * @return {number}
 */
proto.flow.entities.KeyPart.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.flow.entities.KeyPart} returns this
 */
proto.flow.entities.KeyPart.prototype.setType = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional bytes value = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.KeyPart.prototype.getValue = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes value = 2;
 * This is a type-conversion wrapper around `getValue()`
 * @return {string}
 */
proto.flow.entities.KeyPart.prototype.getValue_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getValue()));
};


/**
 * optional bytes value = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getValue()`
 * @return {!Uint8Array}
 */
proto.flow.entities.KeyPart.prototype.getValue_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getValue()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.KeyPart} returns this
 */
proto.flow.entities.KeyPart.prototype.setValue = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.toObject = function(opt_includeInstance) {
  return proto.flow.entities.ExecutionDataTransactionResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.flow.entities.ExecutionDataTransactionResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.ExecutionDataTransactionResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    transactionId: msg.getTransactionId_asB64(),
    failed: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
    computationUsed: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.ExecutionDataTransactionResult}
 */
proto.flow.entities.ExecutionDataTransactionResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.flow.entities.ExecutionDataTransactionResult;
  return proto.flow.entities.ExecutionDataTransactionResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.ExecutionDataTransactionResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.ExecutionDataTransactionResult}
 */
proto.flow.entities.ExecutionDataTransactionResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setTransactionId(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFailed(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setComputationUsed(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.flow.entities.ExecutionDataTransactionResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.ExecutionDataTransactionResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.ExecutionDataTransactionResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTransactionId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      1,
      f
    );
  }
  f = message.getFailed();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getComputationUsed();
  if (f !== 0) {
    writer.writeUint64(
      3,
      f
    );
  }
};


/**
 * optional bytes transaction_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.getTransactionId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * optional bytes transaction_id = 1;
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {string}
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.getTransactionId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getTransactionId()));
};


/**
 * optional bytes transaction_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.getTransactionId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getTransactionId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.ExecutionDataTransactionResult} returns this
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.setTransactionId = function(value) {
  return jspb.Message.setProto3BytesField(this, 1, value);
};


/**
 * optional bool failed = 2;
 * @return {boolean}
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.getFailed = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.flow.entities.ExecutionDataTransactionResult} returns this
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.setFailed = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional uint64 computation_used = 3;
 * @return {number}
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.getComputationUsed = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.flow.entities.ExecutionDataTransactionResult} returns this
 */
proto.flow.entities.ExecutionDataTransactionResult.prototype.setComputationUsed = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


goog.object.extend(exports, proto.flow.entities);
