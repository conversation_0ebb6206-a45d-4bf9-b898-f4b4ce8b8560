{"name": "@onflow/protobuf", "version": "1.3.1", "description": "Access Node Protobuf", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "devDependencies": {"ajv-keywords": "^3.5.2", "colorette": "^2.0.20", "jest": "^29.7.0", "path": "0.12.7", "rechoir": "^0.8.0", "ts-protoc-gen": "0.12.0", "typescript": "3.8.3", "webpack": "^5.95.0", "webpack-cli": "^5.1.4"}, "main": "./dist/index.js", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "webpack --mode=production", "generate": "protoc --plugin='protoc-gen-ts=../../node_modules/.bin/protoc-gen-ts' --js_out='import_style=commonjs,binary:src/generated' --ts_out='service=grpc-web:src/generated' -I ./src/proto ./src/proto/flow/**/*.proto;"}, "dependencies": {"@improbable-eng/grpc-web": "^0.12.0", "elliptic": "^6.5.4", "google-protobuf": "3.11.4"}}