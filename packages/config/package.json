{"name": "@onflow/config", "version": "1.5.2", "description": "Config for FCL-JS", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "devDependencies": {"@babel/preset-typescript": "^7.25.7", "@onflow/fcl-bundle": "1.7.0", "@types/estree": "^1.0.6", "@types/jest": "^29.5.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0", "typescript": "^4.9.5"}, "source": "src/config.ts", "main": "dist/config.js", "module": "dist/config.module.js", "unpkg": "dist/config.umd.js", "types": "types/config.d.ts", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "npm run lint && fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch", "lint": "eslint src"}, "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/util-actor": "1.3.4", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3"}}