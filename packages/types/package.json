{"name": "@onflow/types", "version": "1.4.1", "description": "Utilities to transform javascript values into Cadence understandable values", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "devDependencies": {"@babel/preset-typescript": "^7.25.7", "@onflow/fcl-bundle": "1.7.0", "@types/jest": "^29.5.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0"}, "source": "src/types.ts", "main": "dist/types.js", "module": "dist/types.module.js", "unpkg": "dist/types.umd.js", "types": "types/types.d.ts", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": " npm run lint && fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch", "lint": "eslint src"}, "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/util-logger": "1.3.3"}}