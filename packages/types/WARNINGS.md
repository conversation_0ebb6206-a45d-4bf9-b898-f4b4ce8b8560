# Warnings

## 0002 [U]Int* and Word* as Number

- **Date:** March 4 2022
- **Issue:** [#283](https://github.com/onflow/fcl-js/issues/283)
- **Type:** Deprecation of accepting JavaScript numbers as values for [U]Int* and Word* as Number types.

[U]Int* and Word* types will no longer accept Number as values for them in the near future.

Please only pass String as value for [U]Int* and Word* types.


## 0001 [U]Fix64 as Number

- **Date:** August 19 2020
- **Issue:** [#283](https://github.com/onflow/fcl-js/issues/283)
- **Type:** Deprecation of accepting JavaScript numbers as values for Fix64 and UFix64 types.

Fix64 and UFix64 types will no longer accept numbers as values for them in the near future.

Please pass only strings as values for Fix64 and UFix64 types. 
