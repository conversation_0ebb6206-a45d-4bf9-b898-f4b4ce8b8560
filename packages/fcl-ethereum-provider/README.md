# @onflow/fcl-ethereum-provider

This package exposes a client-side [EIP-1193](https://eips.ethereum.org/EIPS/eip-1193) compatible Ethereum provider that leverages an FCL-authenticated Cadence Owned Account (COA) under the hood. It enables wallets without native EVM capabilities to emulate Ethereum JSON-RPC by delegating signing and COA interactions to FCL.

## Installation

```bash
npm install --save @onflow/fcl-ethereum-provider
```

## Usage

Check out the [documentation](https://developers.flow.com/tools/clients/fcl-js/cross-vm/ethereum-provider) for guides and examples on how to integrate and use this provider in your project.

## License

Apache-2.0