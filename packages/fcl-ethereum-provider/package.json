{"name": "@onflow/fcl-ethereum-provider", "version": "0.0.5", "description": "Ethereum provider for FCL-compatible wallets", "license": "Apache-2.0", "author": "Dapper Labs <<EMAIL>>", "homepage": "https://onflow.org", "repository": {"type": "git", "url": "git+ssh://**************/onflow/flow-js-sdk.git"}, "bugs": {"url": "https://github.com/onflow/flow-js-sdk/issues"}, "devDependencies": {"@babel/preset-typescript": "^7.25.7", "@onflow/fcl-bundle": "1.7.0", "@onflow/typedefs": "^1.6.0", "@types/jest": "^29.5.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0"}, "source": "src/index.ts", "main": "dist/index.js", "module": "dist/index.module.js", "unpkg": "dist/index.umd.js", "types": "types/index.d.ts", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch"}, "dependencies": {"@babel/runtime": "^7.25.7", "@ethersproject/bytes": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@noble/hashes": "^1.7.1", "@onflow/fcl-wc": "6.0.3", "@onflow/rlp": "^1.2.3", "@walletconnect/ethereum-provider": "^2.20.2", "@walletconnect/jsonrpc-http-connection": "^1.0.8", "@walletconnect/jsonrpc-provider": "^1.0.14", "@walletconnect/types": "^2.18.0", "@walletconnect/universal-provider": "^2.20.2", "@walletconnect/utils": "^2.20.2"}, "peerDependencies": {"@onflow/fcl": "1.18.0"}}