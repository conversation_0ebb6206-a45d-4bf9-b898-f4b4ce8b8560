{"name": "@onflow/util-semver", "version": "1.0.3", "description": "A lightweight semver implementation for use in FCL", "main": "dist/index.js", "module": "dist/index.module.js", "source": "src/index.ts", "types": "types/index.d.ts", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch"}, "devDependencies": {"@onflow/fcl-bundle": "1.7.0", "jest": "^29.7.0"}, "dependencies": {"@babel/runtime": "^7.25.7"}, "repository": {"type": "git", "url": "git+https://github.com/onflow/fcl-js.git"}, "author": "Flow Blockchain", "license": "Apache-2.0", "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "homepage": "https://flow.com"}