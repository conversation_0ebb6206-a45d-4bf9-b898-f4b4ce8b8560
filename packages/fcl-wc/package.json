{"name": "@onflow/fcl-wc", "version": "6.0.3", "description": "WalletConnect adapter for FCL", "license": "Apache-2.0", "author": "Flow Foundation", "homepage": "https://flow.com", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-js.git"}, "bugs": {"url": "https://github.com/onflow/fcl-js/issues"}, "files": ["dist"], "source": "src/index.ts", "main": "dist/index.js", "module": "dist/index.module.mjs", "types": "types/index.d.ts", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "fcl-bundle", "test:watch": "jest --watch", "start": "fcl-bundle --watch", "lint": "eslint ."}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/preset-typescript": "^7.25.7", "@onflow/fcl-bundle": "1.7.0", "@onflow/typedefs": "1.6.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^46.10.1", "jest": "^29.7.0", "jest-preset-preact": "^4.1.1"}, "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/config": "1.5.2", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3", "@walletconnect/modal": "^2.7.0", "@walletconnect/types": "^2.20.2", "@walletconnect/universal-provider": "^2.20.2", "@walletconnect/utils": "^2.20.2", "postcss-cli": "^11.0.0", "preact": "^10.24.3", "tailwindcss": "^3.4.14"}, "peerDependencies": {"@onflow/fcl-core": "1.19.0"}}