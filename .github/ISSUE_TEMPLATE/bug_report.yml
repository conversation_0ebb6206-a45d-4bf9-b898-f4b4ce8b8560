name: Reporting a Problem/Bug
description: Reporting a Problem/Bug
title: "[BUG] <title>"
labels: [Bug, Needs Triage]
body:
  - type: markdown
    attributes:
      value: Please fill out the template below to the best of your ability and include a label indicating which tool/service you were working with when you encountered the problem.
  - type: textarea
    attributes:
      label: Current Behavior
      description: A concise description of what you're experiencing.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected Behavior
      description: A concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        1. In this environment...
        2. With this config...
        3. Run '...'
        4. See error...
    validations:
      required: true
  - type: textarea
    attributes:
      label: Environment
      description: |
        examples:
          - **OS**: Ubuntu 20.04
          - **Node**: 13.14.0
          - **npm**: 7.6.3
      value: |
        - OS:
        - Node:
        - npm:
      render: markdown
    validations:
      required: true
  - type: textarea
    attributes:
      label: What are you currently working on that this is blocking?
    validations:
      required: false
