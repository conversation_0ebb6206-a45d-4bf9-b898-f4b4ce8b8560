name: FLOW-JS-SDK Continuous Integration

on:
  pull_request:

jobs:
  test_pull_request:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v1
        with:
          node-version: 18.x

      # This step is VERY important.  Changesets has a bug where peer dependencies will cause major version bumps
      # This script will prevent that from happening.  You must manually edit any changesets PRs which have this issue
      # to correct the version bump.
      #
      # If you wish to allow a major version bump, you will have to override this check, but be VERY careful with it.
      #
      # See:
      # https://github.com/changesets/changesets/pull/1132
      # https://github.com/changesets/changesets/issues/1011
      # https://github.com/changesets/changesets/issues/960
      # https://github.com/changesets/changesets/issues/822
      #- name: Prevent Major Version Bumps
      #  run: node ./.github/scripts/prevent-major-bumps.js

      - run: make ci
