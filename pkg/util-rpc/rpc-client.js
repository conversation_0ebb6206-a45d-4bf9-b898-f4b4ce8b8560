import { RpcError, RpcErrorCode } from "./rpc-error.js";
var ReservedRpcMethods;
(function (ReservedRpcMethods) {
    ReservedRpcMethods["HELLO"] = "rpc_hello";
})(ReservedRpcMethods || (ReservedRpcMethods = {}));
export class RpcClient {
    constructor({ notifications }) {
        this.id = 0;
        this.setSend = () => { };
        this._send = new Promise(resolve => {
            this.setSend = resolve;
        });
        this.peerInfo = new Promise((resolve, reject) => {
            this.resolvePeerInfo = resolve;
            this.rejectPeerInfo = reject;
        });
        this.enabledNotifications = [];
        this.requestHandlers = {};
        this.subscriptions = {};
        this.messageListeners = [];
        this.enabledNotifications = notifications || [];
        this.on(ReservedRpcMethods.HELLO, (info) => {
            this.resolvePeerInfo(info);
            return this.ownInfo();
        });
    }
    connect({ send }) {
        this.setSend(send);
        this.requestWithoutConnection(ReservedRpcMethods.HELLO, this.ownInfo())
            .then(info => {
            this.resolvePeerInfo(info);
        })
            .catch(this.rejectPeerInfo);
    }
    ownInfo() {
        return {
            requests: Object.keys(this.requestHandlers),
            notifications: this.enabledNotifications,
        };
    }
    async send(msg) {
        return (await this._send)(msg);
    }
    receive(msg) {
        if (msg?.jsonrpc !== "2.0") {
            return;
        }
        if ("method" in msg) {
            if ("id" in msg) {
                this.handleRequest(msg);
            }
            else {
                this.handleNotification(msg);
            }
        }
        this.messageListeners.forEach(listener => listener(msg));
    }
    async handleRequest(msg) {
        const handler = this.requestHandlers[msg.method];
        if (handler) {
            try {
                const result = await handler(msg.params);
                this.send({
                    jsonrpc: "2.0",
                    id: msg.id,
                    result,
                });
            }
            catch (error) {
                if (error instanceof RpcError) {
                    this.send({
                        jsonrpc: "2.0",
                        id: msg.id,
                        error: {
                            code: error.code,
                            message: error.message,
                            data: error.data,
                        },
                    });
                }
                else {
                    this.send({
                        jsonrpc: "2.0",
                        id: msg.id,
                        error: {
                            code: RpcErrorCode.INTERNAL_ERROR,
                            message: error?.message,
                        },
                    });
                }
            }
        }
        else {
            this.send({
                jsonrpc: "2.0",
                id: msg.id,
                error: {
                    code: RpcErrorCode.METHOD_NOT_FOUND,
                    message: `Method not found: ${msg.method}`,
                },
            });
        }
    }
    handleNotification(msg) {
        if (this.subscriptions[msg.method]) {
            this.subscriptions[msg.method].forEach(handler => handler(msg.params));
        }
    }
    onMessage(listener) {
        this.messageListeners.push(listener);
        return () => {
            this.messageListeners = this.messageListeners.filter(l => l !== listener);
        };
    }
    async notify(method, params) {
        await this.onceConnected();
        this.send({
            jsonrpc: "2.0",
            method,
            params,
        });
    }
    async request(method, params) {
        await this.onceConnected();
        return this.requestWithoutConnection(method, params);
    }
    async requestWithoutConnection(method, params) {
        const id = this.id++;
        let unsub = () => { };
        const result = new Promise((resolve, reject) => {
            unsub = this.onMessage(msg => {
                if (msg.id === id && ("result" in msg || "error" in msg)) {
                    if (msg.error) {
                        const rpcError = new RpcError(msg.error.code, msg.error.message, msg.error.data);
                        reject(rpcError);
                    }
                    resolve(msg.result);
                }
            });
        }).finally(unsub);
        this.send({
            jsonrpc: "2.0",
            method,
            params,
            id,
        });
        return result;
    }
    on(method, handler) {
        this.requestHandlers[method] = handler;
    }
    subscribe(method, handler) {
        this.subscriptions[method] = this.subscriptions[method] || new Set();
        this.subscriptions[method].add(handler);
    }
    unsubscribe(method, handler) {
        this.subscriptions[method]?.delete(handler);
    }
    async onceConnected() {
        return this.peerInfo.then(() => { });
    }
    async getAvailableRequests() {
        return this.peerInfo.then(info => info.requests);
    }
    async getAvailableNotifications() {
        return this.peerInfo.then(info => info.notifications);
    }
}
