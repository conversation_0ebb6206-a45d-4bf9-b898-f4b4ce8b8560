import { RpcMessage } from "./messages.js";
export type RpcRequest<P, R> = {
    type: "request";
    params: P;
    result: R;
};
export type RpcNotification<P> = {
    type: "notification";
    params: P;
};
type RequestHandler<T = any> = (params: T) => any;
export declare class RpcClient<PeerRequests extends Record<string, RpcRequest<any, any>>, PeerNotifications extends Record<string, RpcNotification<any>>> {
    private id;
    private setSend;
    private _send;
    private resolvePeerInfo;
    private rejectPeerInfo;
    private peerInfo;
    private enabledNotifications;
    private requestHandlers;
    private subscriptions;
    private messageListeners;
    constructor({ notifications }: {
        notifications?: string[];
    });
    connect({ send }: {
        send: (msg: RpcMessage) => void;
    }): void;
    private ownInfo;
    private send;
    receive(msg: RpcMessage): void;
    private handleRequest;
    private handleNotification;
    private onMessage;
    notify<R extends keyof PeerNotifications & string>(method: R, params: PeerNotifications[R]["params"]): Promise<void>;
    request<R extends keyof PeerRequests & string>(method: R, params: PeerRequests[R]["params"]): Promise<PeerRequests[R]["result"]>;
    private requestWithoutConnection;
    on(method: string, handler: RequestHandler): void;
    subscribe<R extends string>(method: R, handler: RequestHandler<any>): void;
    unsubscribe<R extends string>(method: R, handler: RequestHandler<any>): void;
    onceConnected(): Promise<void>;
    getAvailableRequests(): Promise<string[]>;
    getAvailableNotifications(): Promise<string[]>;
}
export {};
//# sourceMappingURL=rpc-client.d.ts.map
