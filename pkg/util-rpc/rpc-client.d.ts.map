{"version": 3, "file": "rpc-client.d.ts", "sourceRoot": "", "sources": ["../../packages/util-rpc/src/rpc-client.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,UAAU,EAA4C,MAAM,YAAY,CAAA;AAGhF,MAAM,MAAM,UAAU,CAAC,CAAC,EAAE,CAAC,IAAI;IAC7B,IAAI,EAAE,SAAS,CAAA;IACf,MAAM,EAAE,CAAC,CAAA;IACT,MAAM,EAAE,CAAC,CAAA;CACV,CAAA;AAED,MAAM,MAAM,eAAe,CAAC,CAAC,IAAI;IAC/B,IAAI,EAAE,cAAc,CAAA;IACpB,MAAM,EAAE,CAAC,CAAA;CACV,CAAA;AAMD,KAAK,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,KAAK,GAAG,CAAA;AAQjD,qBAAa,SAAS,CACpB,YAAY,SAAS,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EACzD,iBAAiB,SAAS,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC;IAE9D,OAAO,CAAC,EAAE,CAAI;IAEd,OAAO,CAAC,OAAO,CAAsD;IACrE,OAAO,CAAC,KAAK,CAEX;IAEF,OAAO,CAAC,eAAe,CAA2B;IAClD,OAAO,CAAC,cAAc,CAAyB;IAC/C,OAAO,CAAC,QAAQ,CAGd;IAEF,OAAO,CAAC,oBAAoB,CAAe;IAC3C,OAAO,CAAC,eAAe,CAA4C;IACnE,OAAO,CAAC,aAAa,CAAsD;IAC3E,OAAO,CAAC,gBAAgB,CAA6B;gBAEzC,EAAC,aAAa,EAAC,EAAE;QAAC,aAAa,CAAC,EAAE,MAAM,EAAE,CAAA;KAAC;IAQvD,OAAO,CAAC,EAAC,IAAI,EAAC,EAAE;QAAC,IAAI,EAAE,CAAC,GAAG,EAAE,UAAU,KAAK,IAAI,CAAA;KAAC;IASjD,OAAO,CAAC,OAAO;YAOD,IAAI;IAIlB,OAAO,CAAC,GAAG,EAAE,UAAU;YAgBT,aAAa;IA4C3B,OAAO,CAAC,kBAAkB;IAM1B,OAAO,CAAC,SAAS;IAOX,MAAM,CAAC,CAAC,SAAS,MAAM,iBAAiB,GAAG,MAAM,EACrD,MAAM,EAAE,CAAC,EACT,MAAM,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAWlC,OAAO,CAAC,CAAC,SAAS,MAAM,YAAY,GAAG,MAAM,EACjD,MAAM,EAAE,CAAC,EACT,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAChC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAKvB,wBAAwB;IAiCtC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc;IAI1C,SAAS,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,cAAc,CAAC,GAAG,CAAC;IAKnE,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,cAAc,CAAC,GAAG,CAAC;IAI/D,aAAa;IAIb,oBAAoB;IAIpB,yBAAyB;CAGhC"}