export var RpcErrorCode;
(function (RpcErrorCode) {
    RpcErrorCode[RpcErrorCode["INVALID_REQUEST"] = -32600] = "INVALID_REQUEST";
    RpcErrorCode[RpcErrorCode["METHOD_NOT_FOUND"] = -32601] = "METHOD_NOT_FOUND";
    RpcErrorCode[RpcErrorCode["INVALID_PARAMS"] = -32602] = "INVALID_PARAMS";
    RpcErrorCode[RpcErrorCode["INTERNAL_ERROR"] = -32603] = "INTERNAL_ERROR";
    RpcErrorCode[RpcErrorCode["PARSE_ERROR"] = -32700] = "PARSE_ERROR";
})(RpcErrorCode || (RpcErrorCode = {}));
export class RpcError extends Error {
    constructor(code, message, data) {
        super(message);
        this.code = code;
        this.message = message;
        this.data = data;
    }
}
