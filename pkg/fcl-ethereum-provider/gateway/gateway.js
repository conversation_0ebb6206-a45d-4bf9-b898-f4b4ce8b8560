import HTTPConnection from "@walletconnect/jsonrpc-http-connection";
import { JsonRpcProvider } from "@walletconnect/jsonrpc-provider";
import { FLOW_CHAINS } from "../constants";
export class Gateway {
    constructor(rpcUrls) {
        this.rpcUrls = rpcUrls;
        this.providers = {};
    }
    async request({ method, params, chainId, }) {
        return this.getProvider(chainId).then(provider => provider.request({ method, params }));
    }
    async getProvider(eip155ChainId) {
        if (this.providers[eip155ChainId]) {
            return this.providers[eip155ChainId];
        }
        let rpcUrl = this.rpcUrls[eip155ChainId];
        if (!rpcUrl) {
            for (const chain of Object.values(FLOW_CHAINS)) {
                if (chain.eip155ChainId === eip155ChainId) {
                    rpcUrl = chain.publicRpcUrl;
                    break;
                }
            }
        }
        if (!rpcUrl) {
            throw new Error(`RPC URL not found for chainId ${eip155ChainId}`);
        }
        const provider = new JsonRpcProvider(new HTTPConnection(rpcUrl));
        this.providers[eip155ChainId] = provider;
        return provider;
    }
}
