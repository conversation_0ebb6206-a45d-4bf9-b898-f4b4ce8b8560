import { Eip1193<PERSON><PERSON>ider, EventCallback, ProviderEvents, ProviderRequest, ProviderResponse } from "./types/provider";
import { RpcProcessor } from "./rpc/rpc-processor";
import { EventDispatcher } from "./events/event-dispatcher";
import { AccountManager } from "./accounts/account-manager";
export declare class FclEthereumProvider implements Eip1193Provider {
    private acountManager;
    private rpcProcessor;
    private eventDispatcher;
    constructor(acountManager: AccountManager, rpcProcessor: RpcProcessor, eventDispatcher: EventDispatcher);
    request<T = unknown>({ method, params, }: ProviderRequest): Promise<ProviderResponse<T>>;
    disconnect(): Promise<void>;
    on<E extends keyof ProviderEvents>(event: E, listener: EventCallback<ProviderEvents[E]>): void;
    removeListener<E extends keyof ProviderEvents>(event: E, listener: EventCallback<ProviderEvents[E]>): void;
}
//# sourceMappingURL=provider.d.ts.map