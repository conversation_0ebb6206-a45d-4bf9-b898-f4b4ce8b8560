import * as fcl from "@onflow/fcl";
import { Eip1193Provider } from "./types/provider";
import { Service } from "@onflow/typedefs";
export type FclProviderConfig = {
    user: typeof fcl.currentUser;
    config: typeof fcl.config;
    service?: Service;
    rpcUrls?: {
        [chainId: number]: string;
    };
};
/**
 * Create a new FCL Ethereum provider
 * @param config - Configuration object
 * @param config.user - The current user
 * @param config.config - The FCL config
 * @param config.service - The service
 * @param config.rpcUrls - The RPC URLs
 * @returns The provider
 * @public
 * @example
 * ```javascript
 * import {createProvider} from "@onflow/fcl-ethereum-provider"
 *
 * const provider = createProvider({
 *  user: fcl.currentUser,
 *  config: fcl.config,
 *  service: fcl.service,
 *  rpcUrls: {
 *   747: "https://mainnet.evm.nodes.onflow.org",
 *  }
 * })
 * ```
 */
export declare function createProvider(config: FclProviderConfig): Eip1193Provider;
//# sourceMappingURL=create-provider.d.ts.map