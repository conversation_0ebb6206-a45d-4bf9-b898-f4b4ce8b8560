import { ACCESS_NODE_API_KEY, FLOW_CHAINS } from "../constants";
import { BehaviorSubject, concat, distinctUntilChanged, filter, firstValueFrom, from, map, of, switchMap, } from "../util/observable";
import * as fcl from "@onflow/fcl";
export class NetworkManager {
    constructor(config) {
        this.$chainIdStore = new BehaviorSubject({
            isLoading: true,
            chainId: null,
            error: null,
        });
        // Map FCL config to behavior subject
        const $config = new BehaviorSubject(null);
        config.subscribe((cfg, err) => {
            if (err) {
                $config.error(err);
            }
            else {
                $config.next(cfg);
            }
        });
        // Bind $network to chainId
        $config
            .pipe(map(cfg => cfg?.[ACCESS_NODE_API_KEY]), distinctUntilChanged(), switchMap(accessNode => concat(of({ isLoading: true }), from((async () => {
            try {
                const flowNetwork = (await fcl.getChainId({
                    node: accessNode,
                }));
                if (!(flowNetwork in FLOW_CHAINS)) {
                    throw new Error("Unknown network");
                }
                const { eip155ChainId: chainId } = FLOW_CHAINS[flowNetwork];
                return { isLoading: false, chainId, error: null };
            }
            catch (error) {
                return { isLoading: false, chainId: null, error };
            }
        })()))))
            .subscribe(this.$chainIdStore);
    }
    get $chainId() {
        return this.$chainIdStore.asObservable();
    }
    async getChainId() {
        const { chainId, error } = await firstValueFrom(this.$chainIdStore.pipe(filter(x => !x.isLoading)));
        if (error) {
            throw error;
        }
        return chainId;
    }
    /**
     * No-op implementation for wallet_addEthereumChain.
     * Since FCL does support dynamic chain additions.
     */
    async addChain(_chainConfig) {
        return null;
    }
    async switchChain(params) {
        const activeChainId = await this.getChainId();
        if (activeChainId === null) {
            throw new Error("No active chain configured.");
        }
        // Convert the chainId from hex (e.g., "0x64") to a number.
        const requestedChainId = parseInt(params.chainId, 16);
        if (requestedChainId !== activeChainId) {
            throw new Error("Network switch error: The requested chain ID does not match the currently configured network.");
        }
        return null;
    }
}
