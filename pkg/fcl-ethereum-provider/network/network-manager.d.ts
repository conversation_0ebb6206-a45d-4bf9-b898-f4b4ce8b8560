import * as fcl from "@onflow/fcl";
import { AddEthereumChainParams, SwitchEthereumChainParams } from "../types/eth";
export type ChainIdStore = {
    isLoading: boolean;
    chainId: number | null;
    error: unknown | null;
};
export declare class NetworkManager {
    private $chainIdStore;
    constructor(config: typeof fcl.config);
    get $chainId(): import("../util/observable").Observable<ChainIdStore>;
    getChainId(): Promise<number | null>;
    /**
     * No-op implementation for wallet_addEthereumChain.
     * Since FCL does support dynamic chain additions.
     */
    addChain(_chainConfig: AddEthereumChainParams): Promise<null>;
    switchChain(params: SwitchEthereumChainParams): Promise<null>;
}
//# sourceMappingURL=network-manager.d.ts.map