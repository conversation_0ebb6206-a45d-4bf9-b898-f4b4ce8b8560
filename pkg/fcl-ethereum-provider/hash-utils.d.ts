import { TypedData } from "./types/eth";
export declare function hashTypedDataLegacy(data: TypedData): string;
/**
 * Hash for `eth_signTypedData_v3`
 *
 * Uses EIP‑712 encoding:
 *   digest = keccak_256( "\x19\x01" || domainSeparator || messageHash )
 */
export declare function hashTypedDataV3(data: TypedData): string;
/**
 * Hash for `eth_signTypedData_v4`
 *
 * For many cases, v3 and v4 yield the same result (if you’re not using arrays or nested dynamic types).
 */
export declare function hashTypedDataV4(data: TypedData): string;
//# sourceMappingURL=hash-utils.d.ts.map