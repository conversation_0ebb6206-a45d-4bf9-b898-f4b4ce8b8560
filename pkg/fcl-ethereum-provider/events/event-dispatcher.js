import { filter, map, Observable, pairwise, skip, takeFirst, } from "../util/observable";
import { formatChainId } from "../util/eth";
import { withPrefix } from "@onflow/fcl";
import { ProviderError, ProviderErrorCode } from "../util/errors";
export class EventDispatcher {
    constructor(accountManager, networkManager) {
        this.$emitters = {
            // Emit changes to the accounts as an accountsChanged event
            accountsChanged: new Observable(subscriber => {
                return accountManager.subscribe(accounts => {
                    subscriber.next(accounts.map(x => withPrefix(x)));
                });
            }),
            // Emit changes to the chainId as a chainChanged event
            chainChanged: networkManager.$chainId.pipe(filter(({ isLoading, error }) => !isLoading && !error), map(({ chainId }) => {
                return formatChainId(chainId);
            }), skip(1)),
            // Emit the first chainId as a connect event
            connect: networkManager.$chainId.pipe(filter(({ isLoading, error }) => !isLoading && !error), map(({ chainId }) => {
                return { chainId: formatChainId(chainId) };
            }), takeFirst()),
            disconnect: networkManager.$chainId.pipe(filter(({ isLoading, error }) => !isLoading && !error), pairwise(), filter(([prev, curr]) => prev.chainId !== null && curr.chainId === null), map(() => {
                return new ProviderError({ code: ProviderErrorCode.Disconnected });
            })),
        };
        this.subscriptions = {
            accountsChanged: new Map(),
            chainChanged: new Map(),
            connect: new Map(),
            disconnect: new Map(),
        };
    }
    // Listen to events
    on(event, listener) {
        const unsub = this.$emitters[event].subscribe(listener);
        this.subscriptions[event].set(listener, unsub);
    }
    // Remove event listeners
    off(event, listener) {
        this.subscriptions[event].get(listener)?.();
        this.subscriptions[event].delete(listener);
    }
}
