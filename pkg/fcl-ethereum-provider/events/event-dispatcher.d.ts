import { EventCallback, ProviderEvents } from "../types/provider";
import { AccountManager } from "../accounts/account-manager";
import { NetworkManager } from "../network/network-manager";
export declare class EventDispatcher {
    private $emitters;
    private subscriptions;
    constructor(accountManager: AccountManager, networkManager: NetworkManager);
    on<E extends keyof ProviderEvents>(event: E, listener: EventCallback<ProviderEvents[E]>): void;
    off<E extends keyof ProviderEvents>(event: E, listener: EventCallback<ProviderEvents[E]>): void;
}
//# sourceMappingURL=event-dispatcher.d.ts.map