{"version": 3, "file": "provider.d.ts", "sourceRoot": "", "sources": ["../../packages/fcl-ethereum-provider/src/provider.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,eAAe,EACf,aAAa,EACb,cAAc,EACd,eAAe,EACf,gBAAgB,EACjB,MAAM,kBAAkB,CAAA;AACzB,OAAO,EAAC,YAAY,EAAC,MAAM,qBAAqB,CAAA;AAChD,OAAO,EAAC,eAAe,EAAC,MAAM,2BAA2B,CAAA;AAEzD,OAAO,EAAC,cAAc,EAAC,MAAM,4BAA4B,CAAA;AAEzD,qBAAa,mBAAoB,YAAW,eAAe;IAEvD,OAAO,CAAC,aAAa;IACrB,OAAO,CAAC,YAAY;IACpB,OAAO,CAAC,eAAe;gBAFf,aAAa,EAAE,cAAc,EAC7B,YAAY,EAAE,YAAY,EAC1B,eAAe,EAAE,eAAe;IAIpC,OAAO,CAAC,CAAC,GAAG,OAAO,EAAE,EACzB,MAAM,EACN,MAAM,GACP,EAAE,eAAe,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAQ3C,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAKjC,EAAE,CAAC,CAAC,SAAS,MAAM,cAAc,EAC/B,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GACzC,IAAI;IAKP,cAAc,CAAC,CAAC,SAAS,MAAM,cAAc,EAC3C,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GACzC,IAAI;CAGR"}