import { withPrefix } from "@onflow/fcl";
export async function ethAccounts(accountManager) {
    const accounts = await accountManager.getAccounts();
    return accounts.map(x => withPrefix(x));
}
export async function ethRequestAccounts(accountManager, chainId) {
    await accountManager.authenticate();
    const accounts = await accountManager.getAndCreateAccounts(chainId);
    return accounts.map(x => withPrefix(x));
}
