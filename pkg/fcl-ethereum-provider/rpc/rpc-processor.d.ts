import { ProviderRequest } from "../types/provider";
import { Gateway } from "../gateway/gateway";
import { AccountManager } from "../accounts/account-manager";
import { NetworkManager } from "../network/network-manager";
export declare class RpcProcessor {
    private gateway;
    private accountManager;
    private networkManager;
    constructor(gateway: Gateway, accountManager: AccountManager, networkManager: NetworkManager);
    handleRequest({ method, params }: ProviderRequest): Promise<any>;
}
//# sourceMappingURL=rpc-processor.d.ts.map