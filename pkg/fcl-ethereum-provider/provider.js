export class FclEthereumProvider {
    constructor(acountManager, rpcProcessor, eventDispatcher) {
        this.acountManager = acountManager;
        this.rpcProcessor = rpcProcessor;
        this.eventDispatcher = eventDispatcher;
    }
    // <PERSON><PERSON> requests
    async request({ method, params, }) {
        if (!method) {
            throw new Error("Method is required");
        }
        const result = await this.rpcProcessor.handleRequest({ method, params });
        return result;
    }
    async disconnect() {
        await this.acountManager.unauthenticate();
    }
    // Listen to events
    on(event, listener) {
        this.eventDispatcher.on(event, listener);
    }
    // Remove event listeners
    removeListener(event, listener) {
        this.eventDispatcher.off(event, listener);
    }
}
