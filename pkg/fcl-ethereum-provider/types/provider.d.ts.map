{"version": 3, "file": "provider.d.ts", "sourceRoot": "", "sources": ["../../../packages/fcl-ethereum-provider/src/types/provider.ts"], "names": [], "mappings": "AACA,OAAO,EAAC,aAAa,EAAC,MAAM,gBAAgB,CAAA;AAE5C,MAAM,MAAM,eAAe,GAAG;IAC5B,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,CAAC,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;CAC7C,CAAA;AAED,MAAM,MAAM,gBAAgB,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,CAAA;AAG7C,MAAM,MAAM,cAAc,GAAG;IAC3B,OAAO,EAAE;QAAC,OAAO,EAAE,MAAM,CAAA;KAAC,CAAA;IAC1B,UAAU,EAAE,aAAa,CAAA;IACzB,YAAY,EAAE,MAAM,CAAA;IACpB,eAAe,EAAE,MAAM,EAAE,CAAA;CAC1B,CAAA;AAGD,MAAM,MAAM,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,CAAA;AAGjD,MAAM,WAAW,eAAe;IAC9B,OAAO,CAAC,CAAC,GAAG,OAAO,EAAE,IAAI,EAAE,eAAe,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAA;IACzE,EAAE,CAAC,CAAC,SAAS,MAAM,cAAc,EAC/B,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GACzC,IAAI,CAAA;IACP,cAAc,CAAC,CAAC,SAAS,MAAM,cAAc,EAC3C,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GACzC,IAAI,CAAA;IACP,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;CAC5B"}