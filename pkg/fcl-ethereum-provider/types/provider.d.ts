import { ProviderError } from "../util/errors";
export type ProviderRequest = {
    method: string;
    params?: unknown[] | Record<string, unknown>;
};
export type ProviderResponse<T = unknown> = T;
export type ProviderEvents = {
    connect: {
        chainId: string;
    };
    disconnect: ProviderError;
    chainChanged: string;
    accountsChanged: string[];
};
export type EventCallback<T> = (event: T) => void;
export interface Eip1193Provider {
    request<T = unknown>(args: ProviderRequest): Promise<ProviderResponse<T>>;
    on<E extends keyof ProviderEvents>(event: E, listener: EventCallback<ProviderEvents[E]>): void;
    removeListener<E extends keyof ProviderEvents>(event: E, listener: EventCallback<ProviderEvents[E]>): void;
    disconnect(): Promise<void>;
}
//# sourceMappingURL=provider.d.ts.map