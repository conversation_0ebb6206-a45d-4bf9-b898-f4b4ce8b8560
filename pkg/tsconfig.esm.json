{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "Node", "lib": ["ES2020", "DOM"], "declaration": true, "declarationMap": true, "outDir": "./", "rootDir": "../packages", "strict": false, "noImplicitAny": false, "noResolve": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "preserveWatchOutput": true, "incremental": true, "tsBuildInfoFile": "./.tsbuildinfo", "baseUrl": ".", "paths": {"@onflow/util-actor": ["./util-actor/index.js"], "@onflow/util-logger": ["./util-logger/util-logger.js"], "@onflow/util-invariant": ["./util-invariant/index.js"], "@onflow/util-address": ["./util-address/index.js"], "@onflow/util-template": ["./util-template/template.js"], "@onflow/util-uid": ["./util-uid/util-uid.js"], "@onflow/util-semver": ["./util-semver/index.js"], "@onflow/util-rpc": ["./util-rpc/index.js"], "@onflow/util-encode-key": ["./util-encode-key/index.js"], "@onflow/config": ["./config/config.js"], "@onflow/rlp": ["./rlp/index.js"], "@onflow/types": ["./types/types.js"], "@onflow/typedefs": ["./typedefs/index.js"], "@onflow/transport-http": ["./transport-http/transport.js"], "@onflow/transport-grpc": ["./transport-grpc/index.js"], "@onflow/sdk": ["./sdk/sdk.js"], "@onflow/fcl-core": ["./fcl-core/fcl-core.js"], "@onflow/fcl": ["./fcl/fcl.js"]}}, "include": ["../packages/*/src/**/*"], "exclude": ["**/*.test.ts", "**/*.test.js", "**/*.spec.ts", "**/*.spec.js", "node_modules", "dist"]}