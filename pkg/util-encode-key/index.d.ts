export declare const ECDSA_P256 = 2;
export declare const ECDSA_secp256k1 = 3;
export declare const SHA2_256 = 1;
export declare const SHA3_256 = 3;
/**
 * Encodes a key into a hex string
 * @param key - The key to encode (DER Hex)
 * @param curve - The curve Flow needs to use with your key [ECDSA_P256|ECDSA_secp256k1]
 * @param hash - The hashing algorythm Flow needs to use with your key [SHA2_256|SHA3_256]
 * @param weight - The weight you want this key to have [Range: 0..1000]
 * @returns The encoded key
 * @throws {Error} - Throws if the key is not a string
 * @throws {Error} - Throws if the key is not in the correct format
 * @throws {Error} - Throws if the curve is not a number
 * @throws {Error} - Throws if the curve is not a valid curve
 * @throws {Error} - Throws if the hash is not a number
 * @throws {Error} - Throws if the hash is not a valid hashing algorithm
 * @throws {Error} - Throws if the weight is not between 0 and 1000
 * @example
 * import {encodeKey, ECDSA_P256, SHA3_256} from "@onflow/util-encode-key"
 * encodeKey("aabbccdd", ECDSA_P256, SHA3_256, 1000) // => "aabbccdd0201000"
 */
export declare function encodeKey(key: string, curve: number, hash: number, weight?: number): string;
//# sourceMappingURL=index.d.ts.map