#!/usr/bin/env node

// 测试 ESM 模块导入
console.log('🧪 Testing ESM modules...\n');

try {
  // 测试 util-invariant
  const { invariant } = await import('./util-invariant/index.js');
  console.log('✅ util-invariant imported successfully');

  // 测试 invariant 函数
  invariant(true, 'This should not throw');
  console.log('✅ invariant function works correctly');

  // 测试 util-logger
  const logger = await import('./util-logger/util-logger.js');
  console.log('✅ util-logger imported successfully');

  // 测试 util-address
  const { withPrefix, sansPrefix } = await import('./util-address/index.js');
  console.log('✅ util-address imported successfully');

  // 测试地址函数
  const address = withPrefix('1234567890abcdef');
  console.log(`✅ withPrefix works: ${address}`);

  const cleanAddress = sansPrefix(address);
  console.log(`✅ sansPrefix works: ${cleanAddress}`);

  // 测试 config
  const { config } = await import('./config/config.js');
  console.log('✅ config imported successfully');

  // 测试 config 功能
  config().put('test.key', 'test.value');
  const testValue = await config().get('test.key');
  console.log(`✅ config functionality works: ${testValue}`);

  // 测试 rlp
  const rlp = await import('./rlp/index.js');
  console.log('✅ rlp imported successfully');

  console.log('\n🎉 All ESM modules imported and tested successfully!');

} catch (error) {
  console.error('❌ Error testing ESM modules:', error);
  process.exit(1);
}
