import { Service } from "@onflow/typedefs";
export declare const fetchFlowWallets: (projectId: string) => Promise<{
    f_type: string;
    f_vsn: string;
    type: string;
    method: string;
    uid: any;
    endpoint: string;
    optIn: boolean;
    provider: {
        address: any;
        name: any;
        icon: any;
        description: any;
        website: any;
        color: any;
        supportEmail: any;
    };
}[]>;
export declare function isAndroid(): boolean;
export declare function isSmallIOS(): boolean;
export declare function isLargeIOS(): boolean;
export declare function isIOS(): boolean;
export declare function isMobile(): boolean;
export declare function openDeeplink(url: string): void;
export declare function shouldDeepLink({ service, user }: {
    service: Service;
    user: any;
}): boolean;
export declare function preloadImage(url?: string | null): void;
//# sourceMappingURL=utils.d.ts.map