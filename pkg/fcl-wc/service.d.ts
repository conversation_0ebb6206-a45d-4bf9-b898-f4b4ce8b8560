import type { FclWalletConnectConfig } from "./fcl-wc";
import { CurrentUser, Service } from "@onflow/typedefs";
import { UniversalProvider } from "@walletconnect/universal-provider";
export declare const makeServicePlugin: (provider: Promise<InstanceType<typeof UniversalProvider> | null>, config?: FclWalletConnectConfig) => {
    name: string;
    f_type: string;
    type: string;
    serviceStrategy: {
        method: string;
        exec: ({ service, body, opts, abortSignal, user, }: {
            service: any;
            body: any;
            opts: any;
            abortSignal?: AbortSignal;
            user: any;
        }) => Promise<any>;
    };
    services: any[];
};
/**
 * Show a notification for a WalletConnect request.
 * @param service - The service that is requesting the user's attention.
 * @param user - The user that is being requested to sign a transaction.
 * @returns A close function to dismiss the notification.
 */
export declare function showWcRequestNotification({ service, user, }: {
    service: Service;
    user: CurrentUser;
}): {
    dismiss: () => void;
};
//# sourceMappingURL=service.d.ts.map