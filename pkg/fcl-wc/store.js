export function createStore(initialState) {
    const subscribers = new Set();
    let state = initialState;
    const subscribe = (subscriber) => {
        subscribers.add(subscriber);
        return () => {
            subscribers.delete(subscriber);
        };
    };
    const setState = (newState) => {
        state = newState;
        subscribers.forEach(subscriber => subscriber(state));
    };
    const getState = () => state;
    return {
        subscribe,
        setState,
        getState,
    };
}
