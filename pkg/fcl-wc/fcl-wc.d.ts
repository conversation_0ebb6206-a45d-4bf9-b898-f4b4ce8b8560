export { getSdkError } from "@walletconnect/utils";
import { CoreTypes } from "@walletconnect/types";
export interface FclWalletConnectConfig {
    projectId: string;
    metadata?: CoreTypes.Metadata;
    includeBaseWC?: boolean;
    wcRequestHook?: any;
    pairingModalOverride?: any;
    wallets?: any[];
    disableNotifications?: boolean;
}
export declare const initLazy: (config: FclWalletConnectConfig) => {
    FclWcServicePlugin: {
        name: string;
        f_type: string;
        type: string;
        serviceStrategy: {
            method: string;
            exec: ({ service, body, opts, abortSignal, user, }: {
                service: any;
                body: any;
                opts: any;
                abortSignal?: AbortSignal;
                user: any;
            }) => Promise<any>;
        };
        services: any[];
    };
    providerPromise: Promise<import("@walletconnect/universal-provider").default>;
};
export declare const init: (config: FclWalletConnectConfig) => Promise<{
    FclWcServicePlugin: {
        name: string;
        f_type: string;
        type: string;
        serviceStrategy: {
            method: string;
            exec: ({ service, body, opts, abortSignal, user, }: {
                service: any;
                body: any;
                opts: any;
                abortSignal?: AbortSignal;
                user: any;
            }) => Promise<any>;
        };
        services: any[];
    };
    client: import("@walletconnect/universal-provider").default;
}>;
export declare function getProvider(): Promise<import("@walletconnect/universal-provider").default>;
export { SERVICE_PLUGIN_NAME } from "./constants";
//# sourceMappingURL=fcl-wc.d.ts.map