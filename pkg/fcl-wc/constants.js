export var FLOW_METHODS;
(function (FLOW_METHODS) {
    FLOW_METHODS["FLOW_AUTHN"] = "flow_authn";
    FLOW_METHODS["FLOW_PRE_AUTHZ"] = "flow_pre_authz";
    FLOW_METHODS["FLOW_AUTHZ"] = "flow_authz";
    FLOW_METHODS["FLOW_USER_SIGN"] = "flow_user_sign";
})(FLOW_METHODS || (FLOW_METHODS = {}));
export var REQUEST_TYPES;
(function (REQUEST_TYPES) {
    REQUEST_TYPES["SESSION_REQUEST"] = "session_proposal";
    REQUEST_TYPES["SIGNING_REQUEST"] = "signing_request";
})(REQUEST_TYPES || (REQUEST_TYPES = {}));
export const SERVICE_PLUGIN_NAME = "fcl-plugin-service-walletconnect";
export const WC_SERVICE_METHOD = "WC/RPC";
