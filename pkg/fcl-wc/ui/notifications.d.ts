import { NotificationInfo } from "../types/types";
/**
 * Show a notification to the user.  Only one notification can be shown at a time and will replace any existing notification.
 */
export declare function showNotification({ title, message, icon, onClick, onDismiss, debounceDelay, }: NotificationInfo & {
    debounceDelay?: number;
}): {
    dismiss: () => void;
};
//# sourceMappingURL=notifications.d.ts.map