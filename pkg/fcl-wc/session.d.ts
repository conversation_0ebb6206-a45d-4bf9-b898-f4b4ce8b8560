import { PairingTypes, SessionTypes } from "@walletconnect/types";
import { UniversalProvider } from "@walletconnect/universal-provider";
export declare function createSessionProposal({ provider, existingPairing, }: {
    provider: InstanceType<typeof UniversalProvider>;
    existingPairing?: PairingTypes.Struct;
}): Promise<{
    uri: string;
    approval: () => Promise<SessionTypes.Struct>;
}>;
export declare const request: ({ method, body, session, provider, isExternal, abortSignal, disableNotifications, }: {
    method: any;
    body: any;
    session: SessionTypes.Struct;
    provider: InstanceType<typeof UniversalProvider>;
    isExternal?: boolean;
    abortSignal?: AbortSignal;
    disableNotifications?: boolean;
}) => Promise<any>;
export declare function makeSessionData(session: SessionTypes.Struct): any[];
//# sourceMappingURL=session.d.ts.map