import AsyncStorage from "@react-native-async-storage/async-storage";
const safeParseJSON = (str) => {
    if (str == null)
        return null;
    try {
        return JSON.parse(str);
    }
    catch (error) {
        return null;
    }
};
export const getAsyncStorage = () => {
    try {
        const ASYNC_STORAGE = {
            can: true,
            get: async (key) => safeParseJSON(await AsyncStorage.getItem(key)),
            put: async (key, value) => await AsyncStorage.setItem(key, JSON.stringify(value)),
        };
        return ASYNC_STORAGE;
    }
    catch (error) {
        return null;
    }
};
