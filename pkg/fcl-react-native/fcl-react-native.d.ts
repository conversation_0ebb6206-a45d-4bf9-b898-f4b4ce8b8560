export { VERSION, query, verifyUserSignatures, serialize, tx, events, pluginRegistry, discovery, t, WalletUtils, AppUtils, InteractionTemplateUtils, getChainId, TestUtils, config, send, decode, account, block, isOk, isBad, why, pipe, build, withPrefix, sansPrefix, display, cadence, cdc, createSignableVoucher, voucherIntercept, voucherToTxId, transaction, script, ping, atBlockHeight, atBlockId, getAccount, getEvents, getEventsAtBlockHeightRange, getEventsAtBlockIds, getBlock, getBlockHeader, getCollection, getTransactionStatus, getTransaction, getNetworkParameters, getNodeVersionInfo, authorizations, authorization, args, arg, proposer, payer, limit, ref, params, param, validator, invariant, subscribeEvents, nodeVersionInfo, TransactionError, } from "@onflow/fcl-core";
export declare const currentUser: any;
export declare const mutate: any;
export declare const authenticate: (opts?: {}) => any;
export declare const unauthenticate: () => any;
export declare const reauthenticate: (opts?: {}) => any;
export declare const signUp: (opts?: {}) => any;
export declare const logIn: (opts?: {}) => any;
export declare const authz: any;
import { useServiceDiscovery, ServiceDiscovery } from "./utils/react-native";
export { useServiceDiscovery, ServiceDiscovery };
export { subscribe } from "@onflow/fcl-core";
export { subscribeRaw } from "@onflow/fcl-core";
//# sourceMappingURL=fcl-react-native.d.ts.map