#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 获取包名参数
const packageName = process.argv[2];
if (!packageName) {
  console.error('Usage: node build-single.js <package-name>');
  process.exit(1);
}

const packagesDir = path.join(__dirname, '../../packages');
const pkgDir = path.join(__dirname, '..');
const packagePath = path.join(packagesDir, packageName);
const outputPath = path.join(pkgDir, packageName);

// 检查包是否存在
if (!fs.existsSync(packagePath) || !fs.existsSync(path.join(packagePath, 'package.json'))) {
  console.error(`Package ${packageName} not found in packages directory`);
  process.exit(1);
}

// 检查是否有 src 目录
const srcPath = path.join(packagePath, 'src');
if (!fs.existsSync(srcPath)) {
  console.error(`Package ${packageName} has no src directory`);
  process.exit(1);
}

console.log(`🔨 Building ${packageName}...`);

try {
  // 清理输出目录中的 js 和 d.ts 文件
  if (fs.existsSync(outputPath)) {
    const files = fs.readdirSync(outputPath);
    files.forEach(file => {
      if (file.endsWith('.js') || file.endsWith('.d.ts') || file.endsWith('.js.map') || file.endsWith('.d.ts.map')) {
        fs.unlinkSync(path.join(outputPath, file));
      }
    });
  }

  // 创建包特定的 tsconfig.esm.json
  const tsconfigPath = path.join(packagePath, 'tsconfig.esm.json');
  const tsconfigContent = {
    "extends": "../../pkg/tsconfig.esm.json",
    "compilerOptions": {
      "outDir": `../../pkg/${packageName}`,
      "rootDir": "./src"
    },
    "include": ["src/**/*"],
    "exclude": [
      "**/*.test.ts",
      "**/*.test.js",
      "**/*.spec.ts",
      "**/*.spec.js"
    ]
  };
  fs.writeFileSync(tsconfigPath, JSON.stringify(tsconfigContent, null, 2));

  // 运行 TypeScript 编译
  console.log(`  📦 Compiling TypeScript...`);
  execSync(`npx tsc -p tsconfig.esm.json`, {
    stdio: 'inherit',
    cwd: packagePath
  });

  // 添加 .js 扩展名
  console.log(`  🔧 Adding .js extensions...`);
  execSync(`npx ts-add-js-extension add --dir=.`, {
    stdio: 'inherit',
    cwd: outputPath
  });

  // 清理临时的 tsconfig.esm.json
  fs.unlinkSync(tsconfigPath);

  console.log(`  ✅ ${packageName} built successfully`);

} catch (error) {
  console.error(`  ❌ Failed to build ${packageName}:`, error.message);

  // 清理可能创建的临时文件
  const tsconfigPath = path.join(packagePath, 'tsconfig.esm.json');
  if (fs.existsSync(tsconfigPath)) {
    fs.unlinkSync(tsconfigPath);
  }

  process.exit(1);
}
