import { EventEmitter } from "events";
import { safeParseJSON } from "./utils";
import { WebSocket } from "../subscribe/websocket";
export class WebsocketError extends Error {
    constructor({ code, reason, message, wasClean, }) {
        const msg = `
      connectWs: connection closed with error${message ? `: ${message}` : ""}
      ${code ? `code: ${code}` : ""}
      ${reason ? `reason: ${reason}` : ""}
      ${wasClean ? `wasClean: ${wasClean}` : ""}
    `;
        super(msg);
        this.name = "WebsocketError";
        this.code = code;
        this.reason = reason;
        this.wasClean = false;
    }
}
export function connectWs({ hostname, path, params, getParams, retryLimit = 5, retryIntervalMs = 1000, }) {
    if (getParams && params) {
        throw new Error("connectWs: cannot specify both params and getParams");
    }
    let outputEmitter = new EventEmitter();
    let retryCount = 0;
    const resolveParams = getParams || (() => params);
    let close = () => { };
    (function connect() {
        let userClosed = false;
        let hasOpened = false;
        // Build a websocket connection with correct protocol & params
        const url = buildConnectionUrl(hostname, path, resolveParams());
        const ws = new WebSocket(url);
        ws.onmessage = function (e) {
            const data = safeParseJSON(e.data);
            if (data) {
                outputEmitter.emit("data", data);
            }
            else {
                outputEmitter.emit("error", new WebsocketError({ message: "invalid JSON data" }));
                this.close();
            }
        };
        ws.onclose = function (e) {
            if (userClosed) {
                outputEmitter.emit("close");
                outputEmitter.removeAllListeners();
                return;
            }
            if (!hasOpened) {
                if (retryCount < retryLimit) {
                    retryCount++;
                    setTimeout(connect, retryIntervalMs);
                }
                else {
                    outputEmitter.emit("error", new WebsocketError({
                        wasClean: e.wasClean,
                        code: e.code,
                        reason: e.reason,
                        message: "failed to connect",
                    }));
                    // Emit close event on next tick so that the error event is emitted first
                    setTimeout(() => {
                        outputEmitter.emit("close");
                        outputEmitter.removeAllListeners();
                    });
                }
            }
            else {
                // If the connection was established before closing, attempt to reconnect
                setTimeout(connect, retryIntervalMs);
            }
        };
        ws.onopen = function () {
            hasOpened = true;
            retryCount = 0;
        };
        close = () => {
            userClosed = true;
            ws.close();
        };
    })();
    return {
        on(event, listener) {
            outputEmitter.on(event, listener);
            return this;
        },
        off(event, listener) {
            outputEmitter.off(event, listener);
            return this;
        },
        close() {
            close();
        },
    };
}
export function buildConnectionUrl(hostname, path, params) {
    const url = new URL(path || "", hostname);
    if (url.protocol === "https:") {
        url.protocol = "wss:";
    }
    else if (url.protocol === "http:") {
        url.protocol = "ws:";
    }
    Object.entries(params || {}).forEach(([key, value]) => {
        if (value) {
            let formattedValue;
            if (Array.isArray(value)) {
                formattedValue = value.join(",");
            }
            else {
                formattedValue = value.toString();
            }
            url.searchParams.append(key, formattedValue);
        }
    });
    return url.toString();
}
