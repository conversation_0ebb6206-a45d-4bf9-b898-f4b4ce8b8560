import { SubscriptionTopic, SubscriptionArgs, Subscription, RawSubscriptionData } from "@onflow/typedefs";
export declare function subscribe<T extends SubscriptionTopic>({ topic, args, onData, onError, }: {
    topic: T;
    args: SubscriptionArgs<T>;
    onData: (data: RawSubscriptionData<T>) => void;
    onError: (error: Error) => void;
}, opts: {
    node: string;
}): Subscription;
//# sourceMappingURL=subscribe.d.ts.map