import { SubscriptionManager } from "./subscription-manager";
import { blocks<PERSON><PERSON><PERSON> } from "./handlers/blocks";
import { blockHeaders<PERSON>andler } from "./handlers/block-headers";
import { blockDigestsHandler } from "./handlers/block-digests";
import { accountStatusesHandler } from "./handlers/account-statuses";
import { transactionStatusesHandler } from "./handlers/transaction-statuses";
import { eventsHandler } from "./handlers/events";
import { combineURLs } from "../utils/combine-urls";
const SUBSCRIPTION_HANDLERS = [
    blocksHandler,
    blockHeadersHandler,
    blockDigestsHandler,
    accountStatusesHandler,
    transactionStatusesHandler,
    eventsHandler,
];
// Map of SubscriptionManager instances by access node URL
let subscriptionManagerMap = new Map();
export function subscribe({ topic, args, onData, onError, }, opts) {
    // Get the SubscriptionManager instance for the access node, or create a new one
    const node = getWsUrl(opts.node);
    const manager = subscriptionManagerMap.get(node) ||
        new SubscriptionManager(SUBSCRIPTION_HANDLERS, { node });
    subscriptionManagerMap.set(node, manager);
    return manager.subscribe({
        topic,
        args,
        onData: onData,
        onError,
    });
}
function getWsUrl(node) {
    const url = new URL(combineURLs(node, "/v1/ws"));
    if (url.protocol === "https:") {
        url.protocol = "wss:";
    }
    else if (url.protocol === "http:") {
        url.protocol = "ws:";
    }
    return url.toString();
}
