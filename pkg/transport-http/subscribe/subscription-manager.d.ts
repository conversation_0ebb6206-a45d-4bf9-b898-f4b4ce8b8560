import { Subscription } from "@onflow/typedefs";
import { SubscriptionHandler } from "./handlers/types";
type InferHandler<T> = T extends SubscriptionHandler<infer H> ? H : never;
export interface SubscriptionManagerConfig {
    /**
     * The URL of the node to connect to
     */
    node: string;
    /**
     * Options for reconnecting to the server
     */
    reconnectOptions?: {
        /**
         * The initial delay in milliseconds before reconnecting
         * @default 500
         */
        initialReconnectDelay?: number;
        /**
         * The maximum number of reconnection attempts
         * @default 5
         */
        reconnectAttempts?: number;
        /**
         * The maximum delay in milliseconds between reconnection attempts
         * @default 5000
         */
        maxReconnectDelay?: number;
    };
}
export declare class SubscriptionManager<Handlers extends SubscriptionHandler<any>[]> {
    private counter;
    private socket;
    private subscriptions;
    private config;
    private reconnectAttempts;
    private handlers;
    private connectPromise;
    private closeConnection;
    constructor(handlers: Handlers, config: SubscriptionManagerConfig);
    subscribe<T extends Handlers[number]>(opts: {
        topic: InferHandler<T>["Topic"];
        args: InferHandler<T>["Args"];
        onData: (data: InferHandler<T>["Data"]) => void;
        onError: (error: Error) => void;
    }): Subscription;
    private _subscribe;
    private unsubscribe;
    private connect;
    private handleSocketError;
    private sendSubscribe;
    private sendUnsubscribe;
    private request;
    private getHandler;
    /**
     * Calculate the backoff interval for reconnection attempts
     * @returns The backoff interval in milliseconds
     */
    private get backoffInterval();
}
export {};
//# sourceMappingURL=subscription-manager.d.ts.map