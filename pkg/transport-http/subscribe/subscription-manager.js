import { Action, SocketError, } from "./models";
import { WebSocket } from "./websocket";
import * as logger from "@onflow/util-logger";
const WS_OPEN = 1;
export class SubscriptionManager {
    constructor(handlers, config) {
        this.counter = 0;
        this.socket = null;
        this.subscriptions = [];
        this.reconnectAttempts = 0;
        this.connectPromise = null;
        this.closeConnection = null;
        this.config = {
            ...config,
            reconnectOptions: {
                initialReconnectDelay: 500,
                reconnectAttempts: 5,
                maxReconnectDelay: 5000,
                ...config.reconnectOptions,
            },
        };
        this.handlers = handlers;
    }
    subscribe(opts) {
        const idPromise = this._subscribe(opts);
        return {
            unsubscribe: () => {
                // Unsubscribe when the ID is available
                idPromise.then(id => id && this.unsubscribe(id));
            },
        };
    }
    async _subscribe(opts) {
        // Get the data provider for the topic
        const topicHandler = this.getHandler(opts.topic);
        const subscriber = topicHandler.createSubscriber(opts.args, opts.onData, opts.onError);
        let sub = null;
        try {
            // Connect the socket if it's not already open
            await this.connect();
            // Track the subscription locally
            sub = {
                id: String(this.counter++),
                topic: opts.topic,
                subscriber: subscriber,
            };
            this.subscriptions.push(sub);
            // Send the subscribe message
            const response = await this.sendSubscribe(sub);
            if (response.error) {
                throw new Error(`Failed to subscribe to topic ${sub.topic}`, {
                    cause: SocketError.fromMessage(response.error),
                });
            }
        }
        catch (e) {
            // Unsubscribe if there was an error
            subscriber.onError(e instanceof Error ? e : new Error(String(e)));
            if (sub)
                this.unsubscribe(sub.id);
            return null;
        }
        return sub.id;
    }
    unsubscribe(id) {
        // Get the subscription
        const sub = this.subscriptions.find(sub => sub.id === id);
        if (!sub)
            return;
        // Remove the subscription
        this.subscriptions = this.subscriptions.filter(sub => sub.id !== id);
        // Close the socket if there are no more subscriptions
        if (this.subscriptions.length === 0) {
            this.closeConnection?.();
            return;
        }
        // Otherwise, the unsubscribe message
        this.sendUnsubscribe(sub).catch(e => {
            console.error(`Error while unsubscribing from topic: ${e}`);
        });
    }
    // Lazy connect to the socket when the first subscription is made
    async connect() {
        if (this.connectPromise) {
            return this.connectPromise;
        }
        this.connectPromise = new Promise((resolve, reject) => {
            // If the socket is already open, do nothing
            if (this.socket?.readyState === WS_OPEN) {
                resolve();
                return;
            }
            this.socket = new WebSocket(this.config.node);
            const onMessage = (event) => {
                const message = JSON.parse(event.data);
                // Error message
                if ("action" in message && message.error) {
                    const sub = this.subscriptions.find(sub => sub.id === message.subscription_id);
                    if (sub) {
                        sub.subscriber.onError(new Error(`Failed to subscribe to topic ${sub.topic}: ${message.error.message}`));
                        // Remove the subscription
                        this.subscriptions = this.subscriptions.filter(sub => sub.id !== message.subscription_id);
                    }
                    return;
                }
                const sub = this.subscriptions.find(sub => sub.id === message.subscription_id);
                if (sub) {
                    if (!("action" in message) && message.subscription_id === sub.id) {
                        sub.subscriber.onData(message.payload);
                    }
                }
            };
            const onClose = () => {
                this.handleSocketError(new Error("WebSocket closed"))
                    .then(() => {
                    resolve();
                })
                    .catch(e => {
                    reject(e);
                });
            };
            const onOpen = () => {
                resolve();
            };
            this.socket.addEventListener("message", onMessage);
            this.socket.addEventListener("close", onClose);
            this.socket.addEventListener("open", onOpen);
            this.closeConnection = () => {
                this.socket?.removeEventListener("message", onMessage);
                this.socket?.removeEventListener("close", onClose);
                this.socket?.removeEventListener("open", onOpen);
                this.socket?.close();
                this.socket = null;
                this.closeConnection = null;
                this.connectPromise = null;
            };
        });
        return this.connectPromise;
    }
    async handleSocketError(error) {
        // Cleanup the connection
        this.closeConnection?.();
        // Validate the number of reconnection attempts
        if (++this.reconnectAttempts >= this.config.reconnectOptions.reconnectAttempts) {
            logger.log({
                level: logger.LEVELS.error,
                title: "WebSocket Error",
                message: `Failed to reconnect to the server after ${this.reconnectAttempts + 1} attempts: ${error}`,
            });
            this.subscriptions.forEach(sub => {
                sub.subscriber.onError(new Error(`Failed to reconnect to the server after ${this.reconnectAttempts + 1} attempts: ${error}`));
            });
            this.subscriptions = [];
            this.reconnectAttempts = 0;
            throw error;
        }
        else {
            logger.log({
                level: logger.LEVELS.warn,
                title: "WebSocket Error",
                message: `WebSocket error, reconnecting in ${this.backoffInterval}ms: ${error}`,
            });
            // Delay the reconnection
            await new Promise(resolve => setTimeout(resolve, this.backoffInterval));
            // Try to reconnect
            await this.connect();
            // Restore subscriptions
            await Promise.all(this.subscriptions.map(async (sub) => {
                await this.sendSubscribe(sub).catch(e => {
                    sub.subscriber.onError(new Error(`Failed to restore subscription: ${e}`));
                    // Remove the subscription
                    this.subscriptions = this.subscriptions.filter(s => s.id !== sub.id);
                });
            }));
            this.reconnectAttempts = 0;
        }
    }
    async sendSubscribe(sub) {
        // Send the subscription message
        const request = {
            action: Action.SUBSCRIBE,
            topic: sub.topic,
            arguments: sub.subscriber.getConnectionArgs(),
            subscription_id: String(sub.id),
        };
        const response = await this.request(request);
        if (response.error) {
            throw new Error(`Failed to subscribe to topic ${sub.topic}`, {
                cause: SocketError.fromMessage(response.error),
            });
        }
        return response;
    }
    async sendUnsubscribe(sub) {
        // Send the unsubscribe message if the subscription has a remote id
        const request = {
            action: Action.UNSUBSCRIBE,
            subscription_id: sub.id,
        };
        this.socket?.send(JSON.stringify(request));
        const response = (await this.request(request));
        if (response.error) {
            throw new Error(`Failed to unsubscribe from topic ${sub.topic}`, {
                cause: SocketError.fromMessage(response.error),
            });
        }
        return response;
    }
    async request(request) {
        let cleanup = () => { };
        return await new Promise((resolve, reject) => {
            if (!this.socket) {
                reject(new Error("WebSocket is not connected"));
                return;
            }
            // Set the cleanup function to remove the event listeners
            cleanup = () => {
                this.socket?.removeEventListener("error", onError);
                this.socket?.removeEventListener("message", onMessage);
                this.socket?.removeEventListener("close", onClose);
            };
            // Bind event listeners
            this.socket.addEventListener("error", onError);
            this.socket.addEventListener("message", onMessage);
            this.socket.addEventListener("close", onClose);
            // Send the request
            this.socket.send(JSON.stringify(request));
            function onError(e) {
                reject(new Error(`WebSocket error: ${e}`));
            }
            function onClose() {
                reject(new Error("WebSocket closed"));
            }
            function onMessage(event) {
                const data = JSON.parse(event.data);
                if (data.subscription_id === request.subscription_id) {
                    resolve(data);
                }
            }
        }).finally(() => {
            cleanup();
        });
    }
    getHandler(topic) {
        const handler = this.handlers.find(handler => handler.topic === topic);
        if (!handler) {
            throw new Error(`No handler found for topic ${topic}`);
        }
        return handler;
    }
    /**
     * Calculate the backoff interval for reconnection attempts
     * @returns The backoff interval in milliseconds
     */
    get backoffInterval() {
        return Math.min(this.config.reconnectOptions.maxReconnectDelay, this.config.reconnectOptions.initialReconnectDelay *
            2 ** this.reconnectAttempts);
    }
}
