{"version": 3, "file": "models.d.ts", "sourceRoot": "", "sources": ["../../../packages/transport-http/src/subscribe/models.ts"], "names": [], "mappings": "AAAA,oBAAY,MAAM;IAChB,kBAAkB,uBAAuB;IACzC,SAAS,cAAc;IACvB,WAAW,gBAAgB;CAC5B;AACD,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,MAAM,CAAA;IACd,eAAe,EAAE,MAAM,CAAA;CACxB;AAED,MAAM,WAAW,mBAAmB;IAClC,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,KAAK,CAAC,EAAE;QACN,IAAI,EAAE,MAAM,CAAA;QACZ,OAAO,EAAE,MAAM,CAAA;KAChB,CAAA;IACD,eAAe,EAAE,MAAM,CAAA;CACxB;AAED,MAAM,WAAW,+BAAgC,SAAQ,kBAAkB;IACzE,MAAM,EAAE,MAAM,CAAC,kBAAkB,CAAA;CAClC;AAED,MAAM,WAAW,gCAAiC,SAAQ,mBAAmB;IAC3E,MAAM,EAAE,MAAM,CAAC,kBAAkB,CAAA;IACjC,aAAa,CAAC,EAAE,iBAAiB,EAAE,CAAA;CACpC;AAED,MAAM,WAAW,uBAAwB,SAAQ,kBAAkB;IACjE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAA;IACxB,KAAK,EAAE,MAAM,CAAA;IACb,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;CAC/B;AAED,MAAM,WAAW,wBAAyB,SAAQ,mBAAmB;IACnE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAA;IACxB,KAAK,EAAE,MAAM,CAAA;CACd;AAED,MAAM,WAAW,yBAA0B,SAAQ,kBAAkB;IACnE,MAAM,EAAE,MAAM,CAAC,WAAW,CAAA;CAC3B;AAED,MAAM,MAAM,0BAA0B,GAAG,mBAAmB,GAAG;IAC7D,MAAM,EAAE,MAAM,CAAC,WAAW,CAAA;IAC1B,EAAE,EAAE,MAAM,CAAA;CACX,CAAA;AAED,MAAM,MAAM,iBAAiB,GAAG;IAC9B,EAAE,EAAE,MAAM,CAAA;IACV,KAAK,EAAE,MAAM,CAAA;IACb,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;CAC/B,CAAA;AAED,MAAM,MAAM,cAAc,GACtB,+BAA+B,GAC/B,uBAAuB,GACvB,yBAAyB,CAAA;AAE7B,MAAM,MAAM,eAAe,GACvB,gCAAgC,GAChC,wBAAwB,GACxB,0BAA0B,CAAA;AAE9B,MAAM,MAAM,uBAAuB,GAAG;IACpC,eAAe,EAAE,MAAM,CAAA;IACvB,OAAO,EAAE,GAAG,CAAA;CACb,CAAA;AACD,qBAAa,WAAY,SAAQ,KAAK;IACpC,IAAI,EAAE,MAAM,CAAA;IAEZ,OAAO;IAMP,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE;QAAC,IAAI,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAC;CAG1D"}