import { RawSubscriptionData, SubscriptionArgs, SubscriptionTopic } from "@onflow/typedefs";
type AccountStatusesArgs = SubscriptionArgs<SubscriptionTopic.ACCOUNT_STATUSES>;
type AccountStatusesData = RawSubscriptionData<SubscriptionTopic.ACCOUNT_STATUSES>;
type AccountStatusesArgsDto = {
    start_block_id?: string;
    start_block_height?: number;
    event_types?: string[];
    addresses?: string[];
    account_addresses?: string[];
};
type AccountStatusesDataDto = {
    block_id: string;
    height: string;
    account_events: {
        [address: string]: {
            type: string;
            transaction_id: string;
            transaction_index: string;
            event_index: string;
            payload: string;
        }[];
    };
    message_index: string;
};
export declare const accountStatusesHandler: import("./types").SubscriptionHandler<{
    Topic: SubscriptionTopic.ACCOUNT_STATUSES;
    Args: AccountStatusesArgs;
    Data: AccountStatusesData;
    ArgsDto: AccountStatusesArgsDto;
    DataDto: AccountStatusesDataDto;
}>;
export {};
//# sourceMappingURL=account-statuses.d.ts.map