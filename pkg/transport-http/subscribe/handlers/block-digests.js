import { SubscriptionTopic, } from "@onflow/typedefs";
import { createSubscriptionHandler } from "./types";
export const blockDigestsHandler = createSubscriptionHandler({
    topic: SubscriptionTopic.BLOCK_DIGESTS,
    createSubscriber: (initialArgs, onData, onError) => {
        let resumeArgs = {
            ...initialArgs,
        };
        return {
            onData(data) {
                // Parse the raw data
                const parsedData = {
                    blockDigest: {
                        id: data.block_id,
                        height: Number(data.height),
                        timestamp: data.timestamp,
                    },
                };
                // Update the resume args
                resumeArgs = {
                    blockStatus: resumeArgs.blockStatus,
                    startBlockId: String(BigInt(data.height) + BigInt(1)),
                };
                onData(parsedData);
            },
            onError(error) {
                onError(error);
            },
            getConnectionArgs() {
                let encodedArgs = {
                    block_status: resumeArgs.blockStatus,
                };
                if ("startBlockHeight" in resumeArgs && resumeArgs.startBlockHeight) {
                    return {
                        ...encodedArgs,
                        start_block_height: resumeArgs.startBlockHeight,
                    };
                }
                if ("startBlockId" in resumeArgs && resumeArgs.startBlockId) {
                    return {
                        ...encodedArgs,
                        start_block_id: resumeArgs.startBlockId,
                    };
                }
                return encodedArgs;
            },
        };
    },
});
