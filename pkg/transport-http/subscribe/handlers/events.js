import { SubscriptionTopic, } from "@onflow/typedefs";
import { createSubscriptionHandler } from "./types";
export const eventsHandler = createSubscriptionHandler({
    topic: SubscriptionTopic.EVENTS,
    createSubscriber: (initialArgs, onData, onError) => {
        let resumeArgs = {
            ...initialArgs,
        };
        return {
            onData(rawData) {
                for (const event of rawData.events) {
                    // Parse the raw data
                    const result = {
                        event: {
                            blockId: rawData.block_id,
                            blockHeight: Number(rawData.block_height),
                            blockTimestamp: rawData.block_timestamp,
                            type: event.type,
                            transactionId: event.transaction_id,
                            transactionIndex: Number(event.transaction_index),
                            eventIndex: Number(event.event_index),
                            payload: JSON.parse(Buffer.from(event.payload, "base64").toString()),
                        },
                    };
                    onData(result);
                }
                // Update the resume args
                resumeArgs = {
                    ...resumeArgs,
                    startHeight: Number(BigInt(rawData.block_height) + BigInt(1)),
                    startBlockId: undefined,
                };
            },
            onError(error) {
                onError(error);
            },
            getConnectionArgs() {
                let encodedArgs = {
                    event_types: resumeArgs.eventTypes,
                    addresses: resumeArgs.addresses,
                    contracts: resumeArgs.contracts,
                };
                if ("startBlockHeight" in resumeArgs && resumeArgs.startBlockHeight) {
                    return {
                        ...encodedArgs,
                        start_block_height: resumeArgs.startBlockHeight,
                    };
                }
                if ("startBlockId" in resumeArgs && resumeArgs.startBlockId) {
                    return {
                        ...encodedArgs,
                        start_block_id: resumeArgs.startBlockId,
                    };
                }
                return encodedArgs;
            },
        };
    },
});
