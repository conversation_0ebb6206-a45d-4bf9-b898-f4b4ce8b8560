import { SubscriptionTopic, } from "@onflow/typedefs";
import { createSubscription<PERSON>andler } from "./types";
export const blocksHandler = createSubscriptionHandler({
    topic: SubscriptionTopic.BLOCKS,
    createSubscriber: (initialArgs, onData, onError) => {
        let resumeArgs = {
            ...initialArgs,
        };
        return {
            onData(data) {
                // Parse the raw data
                const parsedData = {
                    block: {
                        id: data.header.id,
                        parentId: data.header.parent_id,
                        height: Number(data.header.height),
                        timestamp: data.header.timestamp,
                        parentVoterSignature: data.header.parent_voter_signature,
                        collectionGuarantees: data.payload.collection_guarantees.map(guarantee => ({
                            collectionId: guarantee.collection_id,
                            signerIds: guarantee.signer_indices,
                        })),
                        blockSeals: data.payload.block_seals.map(seal => ({
                            blockId: seal.block_id,
                            executionReceiptId: seal.result_id,
                        })),
                    },
                };
                // Update the resume args
                resumeArgs = {
                    blockStatus: resumeArgs.blockStatus,
                    startBlockHeight: Number(BigInt(data.header.height) + BigInt(1)),
                };
                onData(parsedData);
            },
            onError(error) {
                onError(error);
            },
            getConnectionArgs() {
                let encodedArgs = {
                    block_status: resumeArgs.blockStatus,
                };
                if ("startBlockHeight" in resumeArgs && resumeArgs.startBlockHeight) {
                    return {
                        ...encodedArgs,
                        start_block_height: String(resumeArgs.startBlockHeight),
                    };
                }
                if ("startBlockId" in resumeArgs && resumeArgs.startBlockId) {
                    return {
                        ...encodedArgs,
                        start_block_id: resumeArgs.startBlockId,
                    };
                }
                return encodedArgs;
            },
        };
    },
});
