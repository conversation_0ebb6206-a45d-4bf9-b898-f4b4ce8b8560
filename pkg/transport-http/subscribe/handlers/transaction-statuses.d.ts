import { RawSubscriptionData, SubscriptionArgs, SubscriptionTopic } from "@onflow/typedefs";
type TransactionStatusesArgs = SubscriptionArgs<SubscriptionTopic.TRANSACTION_STATUSES>;
type TransactionStatusesData = RawSubscriptionData<SubscriptionTopic.TRANSACTION_STATUSES>;
type TransactionStatusesArgsDto = {
    tx_id: string;
};
type TransactionStatusesDataDto = {
    transaction_result: {
        block_id: string;
        collection_id: string;
        execution: string;
        status: string;
        status_code: 0 | 1;
        error_message: string;
        computation_used: string;
        events: {
            type: string;
            transaction_id: string;
            transaction_index: string;
            event_index: string;
            payload: string;
        }[];
    };
};
export declare const transactionStatusesHandler: import("./types").SubscriptionHandler<{
    Topic: SubscriptionTopic.TRANSACTION_STATUSES;
    Args: TransactionStatusesArgs;
    Data: TransactionStatusesData;
    ArgsDto: TransactionStatusesArgsDto;
    DataDto: TransactionStatusesDataDto;
}>;
export {};
//# sourceMappingURL=transaction-statuses.d.ts.map