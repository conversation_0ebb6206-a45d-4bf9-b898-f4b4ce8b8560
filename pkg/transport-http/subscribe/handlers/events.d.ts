import { SubscriptionTopic, SubscriptionArgs, RawSubscriptionData } from "@onflow/typedefs";
type EventsArgs = SubscriptionArgs<SubscriptionTopic.EVENTS>;
type EventsData = RawSubscriptionData<SubscriptionTopic.EVENTS>;
export type EventsArgsDto = ({
    start_block_id: string;
} | {
    start_block_height: number;
} | {}) & {
    event_types?: string[];
    addresses?: string[];
    contracts?: string[];
};
type EventsDataDto = {
    block_id: string;
    block_height: string;
    block_timestamp: string;
    events: {
        type: string;
        transaction_id: string;
        transaction_index: string;
        event_index: string;
        payload: string;
    }[];
};
export declare const eventsHandler: import("./types").SubscriptionHandler<{
    Topic: SubscriptionTopic.EVENTS;
    Args: EventsArgs;
    Data: EventsData;
    ArgsDto: EventsArgsDto;
    DataDto: EventsDataDto;
}>;
export {};
//# sourceMappingURL=events.d.ts.map