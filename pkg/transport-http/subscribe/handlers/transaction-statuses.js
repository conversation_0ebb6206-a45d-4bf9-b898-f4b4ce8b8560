import { SubscriptionTopic, } from "@onflow/typedefs";
import { createSub<PERSON><PERSON><PERSON><PERSON> } from "./types";
import { <PERSON><PERSON><PERSON> } from "buffer";
const STATUS_MAP = {
    UNKNOWN: 0,
    PENDING: 1,
    FINALIZED: 2,
    EXECUTED: 3,
    SEALED: 4,
    EXPIRED: 5,
};
export const transactionStatusesHandler = createSubscriptionHandler({
    topic: SubscriptionTopic.TRANSACTION_STATUSES,
    createSubscriber: (initialArgs, onData, onError) => {
        let resumeArgs = {
            ...initialArgs,
        };
        return {
            onData(data) {
                // Parse the raw data
                const parsedData = {
                    transactionStatus: {
                        blockId: data.transaction_result.block_id,
                        status: STATUS_MAP[data.transaction_result.status.toUpperCase()],
                        statusString: data.transaction_result.status.toUpperCase(),
                        statusCode: data.transaction_result.status_code,
                        errorMessage: data.transaction_result.error_message,
                        events: data.transaction_result.events.map(event => ({
                            type: event.type,
                            transactionId: event.transaction_id,
                            transactionIndex: Number(event.transaction_index),
                            eventIndex: Number(event.event_index),
                            payload: JSON.parse(Buffer.from(event.payload, "base64").toString()),
                        })),
                    },
                };
                onData(parsedData);
            },
            onError(error) {
                onError(error);
            },
            getConnectionArgs() {
                return {
                    tx_id: resumeArgs.transactionId,
                };
            },
        };
    },
});
