import { SubscriptionArgs, RawSubscriptionData, SubscriptionTopic } from "@onflow/typedefs";
import { BlockArgsDto } from "./types";
type BlockHeadersArgs = SubscriptionArgs<SubscriptionTopic.BLOCK_HEADERS>;
type BlockHeadersData = RawSubscriptionData<SubscriptionTopic.BLOCK_HEADERS>;
type BlockHeadersArgsDto = BlockArgsDto;
type BlockHeadersDataDto = {
    id: string;
    parent_id: string;
    height: string;
    timestamp: string;
    parent_voter_signature: string;
};
export declare const blockHeadersHandler: import("./types").SubscriptionHandler<{
    Topic: SubscriptionTopic.BLOCK_HEADERS;
    Args: BlockHeadersArgs;
    Data: BlockHeadersData;
    ArgsDto: BlockHeadersArgsDto;
    DataDto: BlockHeadersDataDto;
}>;
export {};
//# sourceMappingURL=block-headers.d.ts.map