import { SubscriptionTopic, SubscriptionArgs, RawSubscriptionData } from "@onflow/typedefs";
import { BlockArgsDto } from "./types";
type BlockDigestsArgs = SubscriptionArgs<SubscriptionTopic.BLOCK_DIGESTS>;
type BlockDigestsData = RawSubscriptionData<SubscriptionTopic.BLOCK_DIGESTS>;
type BlockDigestsDataDto = {
    block_id: string;
    height: string;
    timestamp: string;
};
type BlockDigestsArgsDto = BlockArgsDto;
export declare const blockDigestsHandler: import("./types").SubscriptionHandler<{
    Topic: SubscriptionTopic.BLOCK_DIGESTS;
    Args: BlockDigestsArgs;
    Data: BlockDigestsData;
    ArgsDto: BlockDigestsArgsDto;
    DataDto: BlockDigestsDataDto;
}>;
export {};
//# sourceMappingURL=block-digests.d.ts.map