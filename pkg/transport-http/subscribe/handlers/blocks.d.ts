import { RawSubscriptionData, SubscriptionArgs, SubscriptionTopic } from "@onflow/typedefs";
import { BlockArgsDto } from "./types";
type BlocksArgs = SubscriptionArgs<SubscriptionTopic.BLOCKS>;
type BlocksData = RawSubscriptionData<SubscriptionTopic.BLOCKS>;
type BlocksDataDto = {
    header: {
        id: string;
        parent_id: string;
        height: string;
        timestamp: string;
        parent_voter_signature: string;
    };
    payload: {
        collection_guarantees: {
            collection_id: string;
            signer_indices: string[];
        }[];
        block_seals: {
            block_id: string;
            result_id: string;
        }[];
    };
};
export declare const blocksHandler: import("./types").SubscriptionHandler<{
    Topic: SubscriptionTopic.BLOCKS;
    Args: BlocksArgs;
    Data: BlocksData;
    ArgsDto: BlockArgsDto;
    DataDto: BlocksDataDto;
}>;
export {};
//# sourceMappingURL=blocks.d.ts.map