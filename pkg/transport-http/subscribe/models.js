export var Action;
(function (Action) {
    Action["LIST_SUBSCRIPTIONS"] = "list_subscriptions";
    Action["SUBSCRIBE"] = "subscribe";
    Action["UNSUBSCRIBE"] = "unsubscribe";
})(Action || (Action = {}));
export class SocketError extends Error {
    constructor(code, message) {
        super(message);
        this.name = "SocketError";
        this.code = code;
    }
    static fromMessage(error) {
        return new SocketError(error.code, error.message);
    }
}
