import { RainbowKitWalletConnectParameters, Wallet } from "@rainbow-me/rainbowkit";
import { Service } from "@onflow/typedefs";
import * as fcl from "@onflow/fcl";
type FclConnectorOptions = {
    user?: typeof fcl.currentUser;
    config?: typeof fcl.config;
    rpcUrls?: {
        [chainId: number]: string;
    };
    walletConnectParams?: RainbowKitWalletConnectParameters;
    walletDetails: Omit<Wallet, "createConnector">;
    services?: Service[];
};
type DefaultWalletOptions = {
    projectId: string;
};
export declare const createFclConnector: (options: FclConnectorOptions) => ({ projectId }: DefaultWalletOptions) => Wallet;
export {};
//# sourceMappingURL=create-connector.d.ts.map