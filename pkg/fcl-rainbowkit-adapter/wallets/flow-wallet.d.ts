import * as fcl from "@onflow/fcl";
/**
 * Create a connector for the Flow Wallet (currently only supports the extension)
 * @param params - Optional parameters
 * @param params.user - The current user
 * @param params.config - The current config
 * @returns
 */
export declare const flowWallet: (params?: {
    user?: typeof fcl.currentUser;
    config?: typeof fcl.config;
}) => ({ projectId }: {
    projectId: string;
}) => import("@rainbow-me/rainbowkit").Wallet;
//# sourceMappingURL=flow-wallet.d.ts.map