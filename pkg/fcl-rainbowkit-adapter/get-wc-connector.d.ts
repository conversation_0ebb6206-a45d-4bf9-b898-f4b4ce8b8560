import type { CreateConnectorFn } from "wagmi";
import type { RainbowKitWalletConnectParameters, WalletDetailsParams } from "@rainbow-me/rainbowkit";
type CreateConnector = (walletDetails: WalletDetailsParams) => CreateConnectorFn;
interface GetWalletConnectConnectorParams {
    projectId: string;
    walletConnectParameters?: RainbowKitWalletConnectParameters;
}
export declare function getWalletConnectConnector({ projectId, walletConnectParameters, }: GetWalletConnectConnectorParams): CreateConnector;
export {};
//# sourceMappingURL=get-wc-connector.d.ts.map