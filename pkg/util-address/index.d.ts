export declare function sansPrefix(address: null): null;
export declare function sansPrefix(address: string): string;
export declare function sansPrefix(address: string | null): string | null;
export declare function withPrefix(address: null): null;
export declare function withPrefix(address: string): string;
export declare function withPrefix(address: string | null): string | null;
/**
 * @description Adds 0x to address if not already present
 * @param address - Flow address
 * @returns Flow address with 0x prefix
 */
export declare function display(address: string | null): string | null;
//# sourceMappingURL=index.d.ts.map