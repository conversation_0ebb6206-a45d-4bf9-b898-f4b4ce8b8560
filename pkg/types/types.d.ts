export type JsonCdc<L extends string, T> = {
    type: L;
    value: T;
};
type JsonCdcLabel<X extends JsonCdc<string, unknown>> = X extends JsonCdc<infer L, unknown> ? L : never;
export interface TypeDescriptor<T, V extends JsonCdc<string, unknown>> {
    label: JsonCdcLabel<V>;
    asArgument: (x: T) => V;
    asInjection: (x: T) => T;
}
export type TypeDescriptorInput<X extends TypeDescriptor<any, JsonCdc<string, unknown>>> = X extends TypeDescriptor<infer T, JsonCdc<string, unknown>> ? T : never;
export interface PathValue {
    domain: "storage" | "private" | "public";
    identifier: string;
}
/**
 * @deprecated Reference values cannot be imported into the Cadence interpreter, will be removed in future versions
 */
export interface ReferenceValue {
    type: string;
    address: string;
}
/**
 * @deprecated will be removed in v2.0.0
 */
export declare const Identity: {
    label: string;
    asArgument: <T>(v: T) => T;
    asInjection: <T>(v: T) => T;
};
export declare const UInt: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Int: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const UInt8: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Int8: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const UInt16: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Int16: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const UInt32: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Int32: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const UInt64: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Int64: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const UInt128: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Int128: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const UInt256: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Int256: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Word8: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Word16: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Word32: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Word64: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Word128: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Word256: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const UFix64: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const Fix64: TypeDescriptor<string | number, {
    type: string;
    value: string;
}>;
export declare const String: TypeDescriptor<string, {
    type: string;
    value: string;
}>;
export declare const Character: TypeDescriptor<string, {
    type: string;
    value: string;
}>;
export declare const Bool: TypeDescriptor<boolean, {
    type: string;
    value: boolean;
}>;
export declare const Address: TypeDescriptor<string, {
    type: string;
    value: string;
}>;
export declare const Void: TypeDescriptor<null, {
    type: string;
    value: any;
}>;
export declare const Optional: <T extends TypeDescriptor<any, any>>(children: T) => TypeDescriptor<TypeDescriptorInput<T>, {
    type: string;
    value: any;
}>;
/**
 * @deprecated Reference values cannot be imported into the Cadence interpreter, will be removed in future versions
 */
export declare const Reference: TypeDescriptor<ReferenceValue, {
    type: string;
    value: ReferenceValue;
}>;
export declare const _Array: <T extends TypeDescriptor<any, any>>(children?: T[] | T) => TypeDescriptor<TypeDescriptorInput<T>[], {
    type: string;
    value: any[];
}>;
export { _Array as Array };
export declare const Dictionary: <K extends TypeDescriptor<any, any>, V extends TypeDescriptor<any, any>>(children?: {
    key: K;
    value: V;
}[] | {
    key: K;
    value: V;
}) => TypeDescriptor<{
    key: TypeDescriptorInput<K>;
    value: TypeDescriptorInput<V>;
}[] | {
    key: TypeDescriptorInput<K>;
    value: TypeDescriptorInput<V>;
}, {
    type: string;
    value: {
        key: any;
        value: any;
    }[];
}>;
export declare const Event: <V extends TypeDescriptor<any, any>>(id: string, fields?: {
    value: V;
}[] | {
    value: V;
}) => TypeDescriptor<{
    fields: {
        name: string;
        value: TypeDescriptorInput<V>;
    }[];
}, {
    type: string;
    value: {
        id: string;
        fields: {
            name: string;
            value: any;
        }[];
    };
}>;
export declare const Resource: <V extends TypeDescriptor<any, any>>(id: string, fields?: {
    value: V;
}[] | {
    value: V;
}) => TypeDescriptor<{
    fields: {
        name: string;
        value: TypeDescriptorInput<V>;
    }[];
}, {
    type: string;
    value: {
        id: string;
        fields: {
            name: string;
            value: any;
        }[];
    };
}>;
export declare const Struct: <V extends TypeDescriptor<any, any>>(id: string, fields?: {
    value: V;
}[] | {
    value: V;
}) => TypeDescriptor<{
    fields: {
        name: string;
        value: TypeDescriptorInput<V>;
    }[];
}, {
    type: string;
    value: {
        id: string;
        fields: {
            name: string;
            value: any;
        }[];
    };
}>;
export declare const Enum: <V extends TypeDescriptor<any, any>>(id: string, fields?: {
    value: V;
}[] | {
    value: V;
}) => TypeDescriptor<{
    fields: {
        name: string;
        value: TypeDescriptorInput<V>;
    }[];
}, {
    type: string;
    value: {
        id: string;
        fields: {
            name: string;
            value: any;
        }[];
    };
}>;
export declare const Path: TypeDescriptor<PathValue, {
    type: string;
    value: {
        domain: "storage" | "private" | "public";
        identifier: string;
    };
}>;
/**
 * InclusiveRange type
 *
 * @param t - A TypeDescriptor for the type of the range, must be a number (UInt32, Int32, etc.)
 * @returns A TypeDescriptor for an InclusiveRange of the given type
 *
 * @example
 * ```javascript
 * import * as fcl from "@onflow/fcl"
 * import {InclusiveRange, UInt32} from "@onflow/types"
 *
 * const someArg = fcl.arg({start: 1, end: 5, step: 1}, InclusiveRange(UInt32))
 * ```
 */
export declare const InclusiveRange: <T extends TypeDescriptor<any, any>>(t: T) => TypeDescriptor<{
    start: TypeDescriptorInput<T>;
    end: TypeDescriptorInput<T>;
    step: TypeDescriptorInput<T>;
}, {
    type: string;
    value: {
        start: any;
        end: any;
        step: any;
    };
}>;
//# sourceMappingURL=types.d.ts.map