# FCL-JS ESM 构建报告

构建时间: 2025-06-20

## 构建统计

- **总包数**: 26
- **成功构建**: 22
- **跳过构建**: 2 (fcl-bundle, typedefs)
- **构建失败**: 2 (protobuf, transport-grpc)
- **生成文件**: 208 个 .js 文件 + 208 个 .d.ts 文件

## 成功构建的包

| 包名 | 文件数 | 状态 | 备注 |
|------|--------|------|------|
| config | 2 | ✅ | 配置管理包 |
| fcl | 10 | ✅ | 主要 FCL 包 |
| fcl-core | 14 | ✅ | FCL 核心功能 |
| fcl-ethereum-provider | 29 | ✅ | 以太坊提供者适配器 |
| fcl-rainbowkit-adapter | 6 | ✅ | RainbowKit 适配器 |
| fcl-react-native | 2 | ✅ | React Native 支持 |
| fcl-wagmi-adapter | 3 | ✅ | Wagmi 适配器 |
| fcl-wc | 11 | ✅ | WalletConnect 支持 |
| kit | 32 | ✅ | React 组件库 |
| rlp | 1 | ✅ | RLP 编码 |
| sdk | 65 | ✅ | Flow SDK |
| transport-http | 19 | ✅ | HTTP 传输层 |
| types | 1 | ✅ | 类型定义 |
| util-actor | 2 | ✅ | Actor 工具 |
| util-address | 1 | ✅ | 地址工具 |
| util-encode-key | 1 | ✅ | 密钥编码工具 |
| util-invariant | 1 | ✅ | 断言工具 |
| util-logger | 1 | ✅ | 日志工具 |
| util-rpc | 4 | ✅ | RPC 工具 |
| util-semver | 1 | ✅ | 语义版本工具 |
| util-template | 1 | ✅ | 模板工具 |
| util-uid | 1 | ✅ | UID 工具 |

## 跳过的包

| 包名 | 原因 |
|------|------|
| fcl-bundle | 构建工具包，不需要 ESM 编译 |
| typedefs | 仅包含类型定义 |

## 构建失败的包

| 包名 | 失败原因 | 解决方案 |
|------|----------|----------|
| protobuf | parse5 依赖的 TypeScript 语法问题 | 需要更新 TypeScript 版本或调整配置 |
| transport-grpc | 无输入文件 | 检查 src 目录是否存在 |

## 构建配置

- **TypeScript 目标**: ES2020
- **模块格式**: ESNext
- **输出目录**: pkg/(包名)/
- **包含**: src/**/*
- **排除**: 测试文件 (*.test.ts, *.spec.ts)

## 使用方法

### 导入 ESM 模块

```javascript
// 从 pkg 目录导入
import { invariant } from './pkg/util-invariant/index.js'
import { config } from './pkg/config/config.js'
import * as fcl from './pkg/fcl/fcl.js'
```

### 重新构建

```bash
# 构建所有包
npm run build:esm

# 构建单个包
npm run build:esm:single <包名>

# 清理构建产物
npm run clean:esm
```

## 注意事项

1. 所有构建产物都包含 .js 扩展名，符合 ESM 规范
2. 生成的 .d.ts 文件保持类型安全
3. Source maps 已生成，便于调试
4. 构建过程不会修改原始 packages 目录中的任何文件
