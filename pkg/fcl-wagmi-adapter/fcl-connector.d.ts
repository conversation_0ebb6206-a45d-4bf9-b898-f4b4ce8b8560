import { type ProviderConnectInfo } from "viem";
import { createProvider } from "@onflow/fcl-ethereum-provider";
type FclWagmiAdapterParams = Parameters<typeof createProvider>[0] & {
    rdns?: string;
};
export declare function fclWagmiAdapter(params: FclWagmiAdapterParams): import("@wagmi/core").CreateConnectorFn<any, {
    onConnect(connectInfo: ProviderConnectInfo): void;
    onDisplayUri(uri: string): void;
}, Record<string, unknown>>;
export {};
//# sourceMappingURL=fcl-connector.d.ts.map