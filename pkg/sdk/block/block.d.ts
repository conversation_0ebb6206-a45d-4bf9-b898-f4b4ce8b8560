import type { Block } from "@onflow/typedefs";
interface BlockQueryOptions {
    sealed?: boolean;
    height?: number;
    id?: string;
}
/**
 * @description Returns the latest block (optionally sealed or not), by id, or by height
 * @param queryOptions Query parameters
 * @param queryOptions.sealed Whether to query for a sealed block
 * @param queryOptions.height Block height to query
 * @param queryOptions.id Block ID to query
 * @param opts Optional parameters
 * @returns A promise that resolves to a block response
 */
export declare function block({ sealed, id, height }?: BlockQueryOptions, opts?: object): Promise<Block>;
export {};
//# sourceMappingURL=block.d.ts.map