import { SdkTransport, Subscription, SubscriptionTopic } from "@onflow/typedefs";
import { SubscribeParams } from "./types";
/**
 * Subscribe to a topic and decode the data.
 * @param params - The parameters for the subscription.
 * @param opts - Additional options for the subscription.
 * @returns A promise that resolves when the subscription is active.
 */
export declare function subscribe<T extends SubscriptionTopic>({ topic, args, onData, onError }: SubscribeParams<T>, opts?: {
    node?: string;
    transport?: SdkTransport;
}): Subscription;
//# sourceMappingURL=subscribe.d.ts.map