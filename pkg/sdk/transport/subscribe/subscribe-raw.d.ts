import { SdkTransport, SubscriptionTopic } from "@onflow/typedefs";
import { SubscribeRawParams } from "./types";
/**
 * Subscribe to a topic without decoding the data.
 * @param params - The parameters for the subscription.
 * @param opts - Additional options for the subscription.
 * @returns A promise that resolves once the subscription is active.
 */
export declare function subscribeRaw<T extends SubscriptionTopic>({ topic, args, onData, onError }: SubscribeRawParams<T>, opts?: {
    node?: string;
    transport?: SdkTransport;
}): {
    unsubscribe: () => void;
};
//# sourceMappingURL=subscribe-raw.d.ts.map