{"version": 3, "file": "sdk.d.ts", "sourceRoot": "", "sources": ["../../packages/sdk/src/sdk.ts"], "names": [], "mappings": "AAEA,OAAO,EAAC,KAAK,EAAC,MAAM,eAAe,CAAA;AACnC,OAAO,EAAC,OAAO,EAAC,MAAM,mBAAmB,CAAA;AACzC,OAAO,EACL,IAAI,EACJ,SAAS,EACT,YAAY,EACZ,8BAA8B,GAC/B,MAAM,aAAa,CAAA;AACpB,OAAO,EAAC,MAAM,EAAC,MAAM,qBAAqB,CAAA;AAC1C,OAAO,EACL,wBAAwB,EACxB,yBAAyB,EACzB,qBAAqB,GACtB,MAAM,iBAAiB,CAAA;AAGxB,OAAO,EACL,WAAW,EAAE,aAAa;AAC1B,eAAe,EACf,IAAI,EACJ,KAAK,EACL,GAAG,EACH,IAAI,EACJ,GAAG,EACH,GAAG,EACH,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,gBAAgB,EAChB,sBAAsB,EACtB,YAAY,EACZ,WAAW,EACX,MAAM,EACN,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,sBAAsB,EACtB,oBAAoB,GACrB,MAAM,2BAA2B,CAAA;AAClC,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,2BAA2B,CAAA;AAC9D,OAAO,EAAC,eAAe,EAAC,CAAA;AACxB,OAAO,KAAK,EAAC,oBAAoB,EAAC,MAAM,2BAA2B,CAAA;AACnE,OAAO,EAAC,oBAAoB,EAAC,CAAA;AAE7B,OAAO,EAAC,qBAAqB,EAAE,aAAa,EAAC,MAAM,mBAAmB,CAAA;AACtE,OAAO,EAAC,yBAAyB,EAAC,MAAM,gCAAgC,CAAA;AACxE,OAAO,EAAC,QAAQ,IAAI,OAAO,EAAC,MAAM,uBAAuB,CAAA;AACzD,OAAO,EAAC,QAAQ,IAAI,GAAG,EAAC,MAAM,uBAAuB,CAAA;AAErD,OAAO,KAAK,EAAC,OAAO,EAAC,MAAM,gCAAgC,CAAA;AAC3D,OAAO,EAAC,OAAO,EAAC,CAAA;AAGhB,OAAO,EAAC,OAAO,EAAC,MAAM,mBAAmB,CAAA;AACzC,OAAO,EAAC,KAAK,EAAC,MAAM,eAAe,CAAA;AACnC,OAAO,EAAC,eAAe,EAAC,MAAM,uCAAuC,CAAA;AAGrE,OAAO,EAAC,cAAc,EAAE,aAAa,EAAC,MAAM,8BAA8B,CAAA;AAC1E,OAAO,EAAC,aAAa,EAAC,MAAM,+BAA+B,CAAA;AAC3D,OAAO,EAAC,SAAS,EAAC,MAAM,2BAA2B,CAAA;AACnD,OAAO,EAAC,aAAa,EAAC,MAAM,+BAA+B,CAAA;AAC3D,OAAO,EAAC,UAAU,EAAC,MAAM,2BAA2B,CAAA;AACpD,OAAO,EAAC,SAAS,EAAC,MAAM,0BAA0B,CAAA;AAClD,OAAO,EAAC,2BAA2B,EAAC,MAAM,gDAAgD,CAAA;AAC1F,OAAO,EAAC,mBAAmB,EAAC,MAAM,uCAAuC,CAAA;AACzE,OAAO,EAAC,QAAQ,EAAC,MAAM,yBAAyB,CAAA;AAChD,OAAO,EAAC,cAAc,EAAC,MAAM,gCAAgC,CAAA;AAC7D,OAAO,EAAC,aAAa,EAAC,MAAM,8BAA8B,CAAA;AAC1D,OAAO,EAAC,oBAAoB,EAAC,MAAM,sCAAsC,CAAA;AACzE,OAAO,EAAC,cAAc,EAAC,MAAM,+BAA+B,CAAA;AAC5D,OAAO,EAAC,oBAAoB,EAAC,MAAM,sCAAsC,CAAA;AACzE,OAAO,EAAC,kBAAkB,EAAC,MAAM,qCAAqC,CAAA;AACtE,OAAO,EAAC,KAAK,EAAC,MAAM,qBAAqB,CAAA;AACzC,OAAO,EAAC,IAAI,EAAE,GAAG,EAAC,MAAM,yBAAyB,CAAA;AACjD,OAAO,EAAC,QAAQ,EAAC,MAAM,wBAAwB,CAAA;AAC/C,OAAO,EAAC,KAAK,EAAC,MAAM,qBAAqB,CAAA;AACzC,OAAO,EAAC,IAAI,EAAC,MAAM,oBAAoB,CAAA;AACvC,OAAO,EAAC,GAAG,EAAC,MAAM,mBAAmB,CAAA;AACrC,OAAO,EAAC,MAAM,EAAC,MAAM,sBAAsB,CAAA;AAC3C,OAAO,EAAC,WAAW,EAAC,MAAM,2BAA2B,CAAA;AACrD,OAAO,EAAC,SAAS,EAAC,MAAM,yBAAyB,CAAA;AACjD,OAAO,EAAC,SAAS,EAAC,MAAM,yBAAyB,CAAA;AACjD,OAAO,EAAC,gBAAgB,EAAC,MAAM,iCAAiC,CAAA;AAChE,OAAO,EAAC,eAAe,EAAC,MAAM,gCAAgC,CAAA;AAG9D,OAAO,EAAC,cAAc,EAAC,MAAM,2BAA2B,CAAA;AACxD,OAAO,EAAC,yBAAyB,EAAC,MAAM,uCAAuC,CAAA;AAC/E,OAAO,EAAC,6BAA6B,EAAC,MAAM,4CAA4C,CAAA;AACxF,OAAO,EAAC,gBAAgB,EAAC,MAAM,6BAA6B,CAAA;AAC5D,OAAO,EAAC,eAAe,EAAC,MAAM,4BAA4B,CAAA;AAC1D,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAA;AAC5C,OAAO,EAAC,iBAAiB,EAAC,MAAM,8BAA8B,CAAA;AAC9D,OAAO,EAAC,iBAAiB,EAAC,MAAM,8BAA8B,CAAA;AAC9D,OAAO,EAAC,iBAAiB,EAAC,MAAM,gCAAgC,CAAA;AAChE,OAAO,EAAC,uBAAuB,EAAC,MAAM,qCAAqC,CAAA;AAE3E,OAAO,EAAC,MAAM,EAAC,MAAM,gBAAgB,CAAA;AAGrC,eAAO,MAAM,MAAM,WAAY,KAAK,QAOhC,CAAA;AACJ,eAAO,MAAM,KAAK,WAAY,KAAK,QAO/B,CAAA;AAEJ,OAAO,KAAK,SAAS,MAAM,cAAc,CAAA;AACzC,OAAO,EAAC,SAAS,EAAC,CAAA;AAElB,OAAO,EAAC,OAAO,EAAC,MAAM,WAAW,CAAA;AAEjC,OAAO,EAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAC,MAAM,aAAa,CAAA;AAElE,cAAc,kBAAkB,CAAA;AAEhC,OAAO,KAAK,KAAK,MAAM,eAAe,CAAA;AACtC,OAAO,EAAC,KAAK,IAAI,CAAC,EAAC,CAAA"}