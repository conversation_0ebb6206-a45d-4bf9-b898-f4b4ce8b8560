type DecoderFunction = (value: any, decoders: DecoderMap, stack: any[]) => Promise<any>;
interface DecoderMap {
    [key: string]: DecoderFunction;
}
interface DecodeInstructions {
    type: string;
    value?: any;
}
interface FlowEvent {
    type: string;
    transactionId: string;
    transactionIndex: number;
    eventIndex: number;
    payload: DecodeInstructions;
}
interface FlowBlockEvent extends FlowEvent {
    blockId: string;
    blockHeight: number;
    blockTimestamp: string;
}
interface FlowTransactionStatus {
    blockId: string;
    status: number;
    statusCode: number;
    errorMessage: string;
    events: FlowEvent[];
}
interface FlowResponse {
    encodedData?: DecodeInstructions;
    transactionStatus?: FlowTransactionStatus;
    transaction?: any;
    events?: FlowBlockEvent[];
    account?: any;
    block?: any;
    blockHeader?: any;
    blockDigest?: any;
    event?: any;
    accountStatusEvent?: any;
    latestBlock?: any;
    transactionId?: string;
    collection?: any;
    networkParameters?: {
        chainId: string;
    };
    streamConnection?: any;
    heartbeat?: any;
    nodeVersionInfo?: any;
}
/**
 * @description - Decodes a response from Flow into JSON
 * @param decodeInstructions - The response object from Flow
 * @param customDecoders - An object of custom decoders
 * @param stack - The stack of the current decoding
 * @returns - The decoded response
 */
export declare const decode: (decodeInstructions: DecodeInstructions, customDecoders?: DecoderMap, stack?: any[]) => Promise<any>;
export declare const decodeResponse: (response: FlowResponse, customDecoders?: DecoderMap) => Promise<any>;
export {};
//# sourceMappingURL=decode.d.ts.map