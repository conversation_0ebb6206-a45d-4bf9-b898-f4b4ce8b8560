import { InteractionBuilderFn } from "../interaction/interaction";
/**
 * @description A builder function that returns the interaction to get a block header
 * @param isSealed Whether or not the block should be sealed
 * @returns A function that processes an interaction object
 */
export declare function getBlockHeader(isSealed?: boolean | null): InteractionBuilderFn;
//# sourceMappingURL=build-get-block-header.d.ts.map