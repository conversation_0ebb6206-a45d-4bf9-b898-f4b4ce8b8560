import { InteractionBuilderFn } from "../interaction/interaction";
/**
 * @description A builder function that returns the interaction to get events at specific block IDs
 * @param eventType The type of event to get
 * @param blockIds The block IDs to get events from
 * @returns A function that processes an interaction object
 */
export declare function getEventsAtBlockIds(eventType: string, blockIds: string[]): InteractionBuilderFn;
//# sourceMappingURL=build-get-events-at-block-ids.d.ts.map