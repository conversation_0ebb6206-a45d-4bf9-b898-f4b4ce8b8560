import { AccountAuthorization } from "../interaction/interaction";
/**
 * @description A builder function that adds payer account(s) to a transaction
 * @param ax An account address or array of account addresses
 * @returns A function that takes an interaction and returns a new interaction with the payer(s) added
 */
export declare function payer(ax?: AccountAuthorization[]): import("../interaction/interaction").InteractionBuilderFn;
//# sourceMappingURL=build-payer.d.ts.map