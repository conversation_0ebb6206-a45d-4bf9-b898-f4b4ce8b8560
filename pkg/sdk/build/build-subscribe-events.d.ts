import { InteractionBuilderFn } from "../interaction/interaction";
import { EventFilter } from "@onflow/typedefs";
/**
 * @description Subscribe to events with the given filter & parameters
 * @param filter The filter to subscribe to events with
 * @returns A function that processes an interaction object
 */
export declare function subscribeEvents({ startBlockId, startHeight, eventTypes, addresses, contracts, heartbeatInterval, }: EventFilter): InteractionBuilderFn;
//# sourceMappingURL=build-subscribe-events.d.ts.map