import { InteractionBuilderFn } from "../interaction/interaction";
/**
 * @description A builder function that returns the interaction to get the latest block
 * @param isSealed Whether or not the block should be sealed
 * @returns A function that processes an interaction object
 */
export declare function getBlock(isSealed?: boolean | null): InteractionBuilderFn;
//# sourceMappingURL=build-get-block.d.ts.map