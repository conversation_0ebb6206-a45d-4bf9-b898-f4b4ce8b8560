import { InteractionBuilderFn } from "../interaction/interaction";
/**
 * @description A template builder to use a Cadence transaction for an interaction
 * @param args The arguments to pass
 * @returns A function that processes an interaction object
 */
export declare function transaction(...args: [string | TemplateStringsArray, ...any[]]): InteractionBuilderFn;
//# sourceMappingURL=build-transaction.d.ts.map