import { Voucher } from "../encode/encode";
import { Interaction } from "@onflow/typedefs";
export declare function findInsideSigners(ix: Interaction): unknown[];
export declare function findOutsideSigners(ix: Interaction): unknown[];
export declare const createSignableVoucher: (ix: Interaction) => {
    cadence: any;
    refBlock: any;
    computeLimit: any;
    arguments: any;
    proposalKey: {
        address: any;
        keyId: any;
        sequenceNum: any;
    } | {
        address?: undefined;
        keyId?: undefined;
        sequenceNum?: undefined;
    };
    payer: any;
    authorizers: any;
    payloadSigs: {
        address: any;
        keyId: any;
        sig: any;
    }[];
    envelopeSigs: {
        address: any;
        keyId: any;
        sig: any;
    }[];
};
export declare const voucherToTxId: (voucher: Voucher) => string;
//# sourceMappingURL=voucher.d.ts.map