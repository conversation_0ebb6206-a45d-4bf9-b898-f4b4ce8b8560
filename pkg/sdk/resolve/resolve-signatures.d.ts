import { Interaction, InteractionAccount } from "@onflow/typedefs";
export declare function resolveSignatures(ix: Interaction): Promise<Interaction>;
export declare function buildSignable(acct: InteractionAccount, message: string, ix: Interaction): {
    f_type: string;
    f_vsn: string;
    message: string;
    addr: any;
    keyId: any;
    roles: any;
    cadence: any;
    args: any;
    data: {};
    interaction: Interaction;
    voucher: {
        cadence: any;
        refBlock: any;
        computeLimit: any;
        arguments: any;
        proposalKey: {
            address: any;
            keyId: any;
            sequenceNum: any;
        } | {
            address?: undefined;
            keyId?: undefined;
            sequenceNum?: undefined;
        };
        payer: any;
        authorizers: any;
        payloadSigs: {
            address: any;
            keyId: any;
            sig: any;
        }[];
        envelopeSigs: {
            address: any;
            keyId: any;
            sig: any;
        }[];
    };
};
//# sourceMappingURL=resolve-signatures.d.ts.map