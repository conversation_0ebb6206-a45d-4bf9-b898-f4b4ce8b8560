import { Interaction, InteractionAccount } from "@onflow/typedefs";
export declare function buildPreSignable(acct: Partial<InteractionAccount>, ix: Interaction): {
    f_type: string;
    f_vsn: string;
    roles: any;
    cadence: any;
    args: any;
    data: {};
    interaction: Interaction;
    voucher: {
        cadence: any;
        refBlock: any;
        computeLimit: any;
        arguments: any;
        proposalKey: {
            address: any;
            keyId: any;
            sequenceNum: any;
        } | {
            address?: undefined;
            keyId?: undefined;
            sequenceNum?: undefined;
        };
        payer: any;
        authorizers: any;
        payloadSigs: {
            address: any;
            keyId: any;
            sig: any;
        }[];
        envelopeSigs: {
            address: any;
            keyId: any;
            sig: any;
        }[];
    };
};
export declare function resolveAccounts(ix: Interaction, opts?: Record<string, any>): Promise<Interaction>;
//# sourceMappingURL=resolve-accounts.d.ts.map