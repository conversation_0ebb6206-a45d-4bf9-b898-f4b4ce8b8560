{"version": 3, "file": "interaction.d.ts", "sourceRoot": "", "sources": ["../../../packages/sdk/src/interaction/interaction.ts"], "names": [], "mappings": "AAIA,OAAO,EACL,eAAe,EACf,WAAW,EACX,kBAAkB,EAInB,MAAM,kBAAkB,CAAA;AACzB,OAAO,EAAC,mBAAmB,EAAE,cAAc,EAAC,MAAM,eAAe,CAAA;AAEjE,MAAM,MAAM,eAAe,GAAG,CAAC,IAAI,EAAE,kBAAkB,KAAK,kBAAkB,CAAA;AAC9E,MAAM,MAAM,oBAAoB,GAC5B,CAAC,eAAe,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC,GAC/C,OAAO,CAAC,kBAAkB,CAAC,CAAA;AAE/B,KAAK,eAAe,CAAC,CAAC,SAAS,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI;IACzD,KAAK,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAA;IAC7B,KAAK,EAAE,CAAC,CAAA;CACT,CAAA;AAED,OAAO,EAAC,eAAe,EAAC,CAAA;AAExB,MAAM,MAAM,oBAAoB,GAAG,CACjC,EAAE,EAAE,WAAW,KACZ,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAAA;AAkFvC,eAAO,MAAM,eAAe,mBAAoC,CAAA;AAChE;;GAEG;AACH,eAAO,MAAM,WAAW,mBASvB,CAAA;AAED,eAAO,MAAM,QAAQ,MAAO,GAAG,gBAAuC,CAAA;AACtE,eAAO,MAAM,OAAO,MAAO,GAAG,eAAiC,CAAA;AAC/D,eAAO,MAAM,KAAK,MAAO,GAAG,6BACS,CAAA;AACrC,eAAO,MAAM,MAAM,MAAO,GAAG,cAAyB,CAAA;AACtD,eAAO,MAAM,IAAI,MAAO,GAAG,kBAA2C,CAAA;AAEtE,eAAO,MAAM,aAAa,OAAQ,OAAO,YAIxC,CAAA;AAED,eAAO,MAAM,EAAE,kCAGd,CAAA;AAED,eAAO,MAAM,GAAG,4BAA6B,MAAM,gBAIlD,CAAA;AAqBD,UAAU,gBAAgB;IACxB,IAAI,CAAC,EAAE,eAAe,GAAG,IAAI,CAAA;CAC9B;AAED,eAAO,MAAM,WAAW,0BAA6C,CAAA;AAErE,eAAO,MAAM,WAAW,SACf,oBAAoB,SAAQ,gBAAgB,qCA+ClD,CAAA;AAEH,eAAO,MAAM,YAAY,QAAS,OAAO,MAAM,EAAE,GAAG,CAAC,qCAepD,CAAA;AAED,eAAO,MAAM,WAAW,kCAAuD,CAAA;AAC/E,eAAO,MAAM,UAAU,kCAAuD,CAAA;AAC9E,eAAO,MAAM,eAAe,kCAE3B,CAAA;AACD,eAAO,MAAM,wBAAwB,kCAEpC,CAAA;AACD,eAAO,MAAM,kBAAkB,kCAE9B,CAAA;AACD,eAAO,MAAM,cAAc,kCAE1B,CAAA;AACD,eAAO,MAAM,aAAa,kCAEzB,CAAA;AACD,eAAO,MAAM,QAAQ,kCAAuD,CAAA;AAC5E,eAAO,MAAM,YAAY,kCAExB,CAAA;AACD,eAAO,MAAM,kBAAkB,kCAE9B,CAAA;AACD,eAAO,MAAM,iBAAiB,kCAE7B,CAAA;AACD,eAAO,MAAM,wBAAwB,kCAEpC,CAAA;AACD,eAAO,MAAM,mBAAmB,kCAE/B,CAAA;AACD,eAAO,MAAM,qBAAqB,kCAEjC,CAAA;AAID,eAAO,MAAM,SAAS,8BAAmD,CAAA;AACzE,eAAO,MAAM,QAAQ,8BAAmD,CAAA;AACxE,eAAO,MAAM,aAAa,8BAAmD,CAAA;AAC7E,eAAO,MAAM,sBAAsB,8BAElC,CAAA;AACD,eAAO,MAAM,gBAAgB,8BAE5B,CAAA;AACD,eAAO,MAAM,YAAY,8BAAoD,CAAA;AAC7E,eAAO,MAAM,WAAW,8BAAoD,CAAA;AAC5E,eAAO,MAAM,MAAM,8BAAmD,CAAA;AACtE,eAAO,MAAM,UAAU,8BAAoD,CAAA;AAC3E,eAAO,MAAM,gBAAgB,8BAE5B,CAAA;AACD,eAAO,MAAM,eAAe,8BAAoD,CAAA;AAChF,eAAO,MAAM,sBAAsB,8BAElC,CAAA;AACD,eAAO,MAAM,oBAAoB,8BAEhC,CAAA;AACD,eAAO,MAAM,iBAAiB,8BAE7B,CAAA;AAED,eAAO,MAAM,IAAI,8BACmB,CAAA;AACpC,eAAO,MAAM,KAAK,8BACmB,CAAA;AACrC,eAAO,MAAM,GAAG,0BAAyC,CAAA;AAEzD,eAAO,MAAM,SAAS,YAAoB,OAAO,MAAM,EAAE,GAAG,CAAC,YACX,CAAA;AAClD,eAAO,MAAM,UAAU,aAAoB,OAAO,MAAM,EAAE,GAAG,CAAC,YACV,CAAA;AAUpD,KAAK,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;AAqBrC;;;GAGG;AACH,iBAAS,IAAI,CAAC,GAAG,EAAE,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,GAAG,oBAAoB,CAAA;AAC1E,iBAAS,IAAI,CACX,EAAE,EAAE,YAAY,CAAC,WAAW,CAAC,EAC7B,GAAG,EAAE,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,GACpC,OAAO,CAAC,WAAW,CAAC,CAAA;AAavB,OAAO,EAAC,IAAI,EAAC,CAAA;AAIb,eAAO,MAAM,GAAG,yBAET,MAAM,aACD,GAAG,QAGd,CAAA;AAED,eAAO,MAAM,GAAG,QAAS,MAAM,SAAS,GAAG,qCAG1C,CAAA;AAED,eAAO,MAAM,MAAM,WACR,MAAM,6BAA4B,GAAG,EAAE,iDAI/C,CAAA;AAEH,eAAO,MAAM,OAAO,QAAS,MAAM,qCAGlC,CAAA"}