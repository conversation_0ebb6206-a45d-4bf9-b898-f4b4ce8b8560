import { TransactionRole, Interaction, InteractionAccount } from "@onflow/typedefs";
import { TypeDescriptorInput, TypeDescriptor } from "@onflow/types";
export type AuthorizationFn = (acct: InteractionAccount) => InteractionAccount;
export type AccountAuthorization = (AuthorizationFn & Partial<InteractionAccount>) | Partial<InteractionAccount>;
type CadenceArgument<T extends TypeDescriptor<any, any>> = {
    value: TypeDescriptorInput<T>;
    xform: T;
};
export { CadenceArgument };
export type InteractionBuilderFn = (ix: Interaction) => Interaction | Promise<Interaction>;
export declare const initInteraction: () => Interaction;
/**
 * @deprecated
 */
export declare const interaction: () => Interaction;
export declare const isNumber: (d: any) => d is number;
export declare const isArray: (d: any) => d is any[];
export declare const isObj: (d: any) => d is Record<string, any>;
export declare const isNull: (d: any) => d is null;
export declare const isFn: (d: any) => d is Function;
export declare const isInteraction: (ix: unknown) => boolean;
export declare const Ok: (ix: Interaction) => Interaction;
export declare const Bad: (ix: Interaction, reason: string) => Interaction;
interface IPrepAccountOpts {
    role?: TransactionRole | null;
}
export declare const initAccount: () => InteractionAccount;
export declare const prepAccount: (acct: AccountAuthorization, opts?: IPrepAccountOpts) => (ix: Interaction) => Interaction;
export declare const makeArgument: (arg: Record<string, any>) => (ix: Interaction) => Interaction;
export declare const makeUnknown: (ix: Interaction) => Interaction;
export declare const makeScript: (ix: Interaction) => Interaction;
export declare const makeTransaction: (ix: Interaction) => Interaction;
export declare const makeGetTransactionStatus: (ix: Interaction) => Interaction;
export declare const makeGetTransaction: (ix: Interaction) => Interaction;
export declare const makeGetAccount: (ix: Interaction) => Interaction;
export declare const makeGetEvents: (ix: Interaction) => Interaction;
export declare const makePing: (ix: Interaction) => Interaction;
export declare const makeGetBlock: (ix: Interaction) => Interaction;
export declare const makeGetBlockHeader: (ix: Interaction) => Interaction;
export declare const makeGetCollection: (ix: Interaction) => Interaction;
export declare const makeGetNetworkParameters: (ix: Interaction) => Interaction;
export declare const makeSubscribeEvents: (ix: Interaction) => Interaction;
export declare const makeGetNodeVerionInfo: (ix: Interaction) => Interaction;
export declare const isUnknown: (ix: Interaction) => boolean;
export declare const isScript: (ix: Interaction) => boolean;
export declare const isTransaction: (ix: Interaction) => boolean;
export declare const isGetTransactionStatus: (ix: Interaction) => boolean;
export declare const isGetTransaction: (ix: Interaction) => boolean;
export declare const isGetAccount: (ix: Interaction) => boolean;
export declare const isGetEvents: (ix: Interaction) => boolean;
export declare const isPing: (ix: Interaction) => boolean;
export declare const isGetBlock: (ix: Interaction) => boolean;
export declare const isGetBlockHeader: (ix: Interaction) => boolean;
export declare const isGetCollection: (ix: Interaction) => boolean;
export declare const isGetNetworkParameters: (ix: Interaction) => boolean;
export declare const isGetNodeVersionInfo: (ix: Interaction) => boolean;
export declare const isSubscribeEvents: (ix: Interaction) => boolean;
export declare const isOk: (ix: Interaction) => boolean;
export declare const isBad: (ix: Interaction) => boolean;
export declare const why: (ix: Interaction) => any;
export declare const isAccount: (account: Record<string, any>) => boolean;
export declare const isArgument: (argument: Record<string, any>) => boolean;
type MaybePromise<T> = T | Promise<T>;
/**
 * @description Async pipe function to compose interactions
 * @returns An interaction object
 */
declare function pipe(fns: (InteractionBuilderFn | false)[]): InteractionBuilderFn;
declare function pipe(ix: MaybePromise<Interaction>, fns: (InteractionBuilderFn | false)[]): Promise<Interaction>;
export { pipe };
export declare const get: (ix: Interaction, key: string, fallback?: any) => any;
export declare const put: (key: string, value: any) => (ix: Interaction) => Interaction;
export declare const update: <T>(key: string, fn?: (v: T | T[], ...args: any[]) => T | T[]) => (ix: Interaction) => Interaction;
export declare const destroy: (key: string) => (ix: Interaction) => Interaction;
//# sourceMappingURL=interaction.d.ts.map