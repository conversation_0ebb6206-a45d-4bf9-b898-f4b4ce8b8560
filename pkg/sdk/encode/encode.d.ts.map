{"version": 3, "file": "encode.d.ts", "sourceRoot": "", "sources": ["../../../packages/sdk/src/encode/encode.ts"], "names": [], "mappings": "AAIA,eAAO,MAAM,wBAAwB,OAAQ,WAAW,WACI,CAAA;AAC5D,eAAO,MAAM,yBAAyB,OAAQ,WAAW,WACI,CAAA;AAC7D,eAAO,MAAM,qBAAqB,YAAa,OAAO,WACR,CAAA;AAuL9C,UAAU,eAAe;IACvB,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,MAAM,CAAA;CACd;AAED,UAAU,kBAAkB;IAC1B,OAAO,EAAE,MAAM,CAAA;IACf,KAAK,EAAE,MAAM,GAAG,IAAI,CAAA;IACpB,WAAW,EAAE,MAAM,GAAG,IAAI,CAAA;CAC3B;AAED,UAAU,GAAG;IACX,OAAO,EAAE,MAAM,CAAA;IACf,KAAK,EAAE,MAAM,GAAG,MAAM,CAAA;IACtB,GAAG,EAAE,MAAM,CAAA;CACZ;AAED,MAAM,WAAW,sBAAsB;IACrC,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IACvB,WAAW,CAAC,EAAE,MAAM,CAAA;CACrB;AACD,MAAM,WAAW,WAAW;IAC1B,OAAO,EAAE,MAAM,CAAA;IACf,QAAQ,EAAE,MAAM,CAAA;IAChB,YAAY,EAAE,MAAM,CAAA;IACpB,SAAS,EAAE,eAAe,EAAE,CAAA;IAC5B,WAAW,EAAE,sBAAsB,CAAA;IACnC,KAAK,EAAE,MAAM,CAAA;IACb,WAAW,EAAE,MAAM,EAAE,CAAA;IACrB,WAAW,CAAC,EAAE,GAAG,EAAE,CAAA;IACnB,YAAY,CAAC,EAAE,sBAAsB,EAAE,CAAA;CACxC;AAED,MAAM,WAAW,OAAO;IACtB,OAAO,EAAE,MAAM,CAAA;IACf,QAAQ,EAAE,MAAM,CAAA;IAChB,YAAY,EAAE,MAAM,CAAA;IACpB,SAAS,EAAE,eAAe,EAAE,CAAA;IAC5B,WAAW,EAAE,kBAAkB,CAAA;IAC/B,KAAK,EAAE,MAAM,CAAA;IACb,WAAW,EAAE,MAAM,EAAE,CAAA;IACrB,WAAW,EAAE,GAAG,EAAE,CAAA;IAClB,YAAY,EAAE,GAAG,EAAE,CAAA;CACpB"}