{"name": "fcl-js-esm-build", "version": "1.0.0", "description": "ESM build configuration for FCL-JS packages", "type": "module", "private": true, "scripts": {"build:esm": "node scripts/build-esm.js", "build:single": "node scripts/build-single.js", "clean": "find . -name '*.js' -not -path './scripts/*' -delete && find . -name '*.d.ts' -delete && find . -name '*.js.map' -delete && find . -name '*.d.ts.map' -delete", "clean:all": "rm -rf */!(scripts) && mkdir -p config fcl fcl-bundle fcl-core fcl-ethereum-provider fcl-rainbowkit-adapter fcl-react-native fcl-wagmi-adapter fcl-wc kit protobuf rlp sdk transport-grpc transport-http typedefs types util-actor util-address util-encode-key util-invariant util-logger util-rpc util-semver util-template util-uid"}, "keywords": ["flow", "fcl", "esm", "typescript"], "author": "Flow Foundation", "license": "Apache-2.0"}