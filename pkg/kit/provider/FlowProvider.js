import React, { useEffect, useState } from "react";
import * as fcl from "@onflow/fcl";
import { FlowConfigContext } from "../core/context";
import { QueryClient } from "@tanstack/react-query";
import { FlowQueryClientProvider } from "./FlowQueryClient";
import { deepEqual } from "../utils/deepEqual";
const mappings = [
    { fcl: "accessNode.api", typed: "accessNodeUrl" },
    { fcl: "app.detail.title", typed: "appDetailTitle" },
    { fcl: "app.detail.icon", typed: "appDetailIcon" },
    { fcl: "app.detail.description", typed: "appDetailDescription" },
    { fcl: "app.detail.url", typed: "appDetailUrl" },
    { fcl: "discovery.wallet", typed: "discoveryWallet" },
    { fcl: "discovery.wallet.method", typed: "discoveryWalletMethod" },
    { fcl: "fcl.limit", typed: "fclLimit" },
    { fcl: "flow.network", typed: "flowNetwork" },
    { fcl: "service.OpenID.scopes", typed: "serviceOpenIdScopes" },
    { fcl: "walletconnect.projectId", typed: "walletconnectProjectId" },
    {
        fcl: "walletconnect.disableNotifications",
        typed: "walletconnectDisableNotifications",
    },
];
// Map typed keys to FCL config keys
const typedToFcl = mappings.reduce((acc, mapping) => {
    acc[mapping.typed] = mapping.fcl;
    return acc;
}, {});
// Map FCL config keys to typed keys
const fclToTyped = mappings.reduce((acc, mapping) => {
    acc[mapping.fcl] = mapping.typed;
    return acc;
}, {});
/**
 * Converts typed config into FCL-style config.
 */
function convertTypedConfig(typedConfig) {
    const fclConfig = {};
    for (const key in typedConfig) {
        const value = typedConfig[key];
        if (value !== undefined) {
            const fclKey = typedToFcl[key];
            if (fclKey) {
                fclConfig[fclKey] = value;
            }
        }
    }
    return fclConfig;
}
/**
 * Converts FCL-style config into typed config.
 */
function mapConfig(original) {
    const mapped = {};
    for (const [fclKey, value] of Object.entries(original)) {
        if (fclKey in fclToTyped) {
            mapped[fclToTyped[fclKey]] = value;
        }
    }
    return mapped;
}
const defaultQueryOptions = {
    queries: {
        retry: false,
        staleTime: 0,
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        refetchIntervalInBackground: false,
    },
};
export function FlowProvider({ config: initialConfig = {}, queryClient: _queryClient, flowJson, children, }) {
    const [queryClient] = useState(() => _queryClient ?? new QueryClient({ defaultOptions: defaultQueryOptions }));
    const [flowConfig, setFlowConfig] = useState(null);
    const [isFlowJsonLoaded, setIsFlowJsonLoaded] = useState(false);
    useEffect(() => {
        const initializeFCL = async () => {
            try {
                if (Object.keys(initialConfig).length > 0) {
                    const fclConfig = convertTypedConfig(initialConfig);
                    if (flowJson) {
                        await fcl.config(fclConfig).load({ flowJSON: flowJson });
                    }
                    else {
                        fcl.config(fclConfig);
                    }
                }
                else if (flowJson) {
                    await fcl.config().load({ flowJSON: flowJson });
                }
                setIsFlowJsonLoaded(true);
            }
            catch (error) {
                setIsFlowJsonLoaded(true);
            }
        };
        initializeFCL();
        const unsubscribe = fcl.config().subscribe(latest => {
            const newConfig = mapConfig(latest || {});
            setFlowConfig(prev => {
                if (prev && deepEqual(prev, newConfig)) {
                    return prev;
                }
                return newConfig;
            });
        });
        return () => unsubscribe();
    }, [initialConfig, flowJson]);
    if (!flowConfig || !isFlowJsonLoaded) {
        return null;
    }
    return (<FlowQueryClientProvider queryClient={queryClient}>
      <FlowConfigContext.Provider value={flowConfig}>
        {children}
      </FlowConfigContext.Provider>
    </FlowQueryClientProvider>);
}
