import React, { PropsWithChildren } from "react";
import { FlowConfig } from "../core/context";
import { QueryClient } from "@tanstack/react-query";
interface FlowProviderProps {
    config?: FlowConfig;
    queryClient?: QueryClient;
    flowJson?: Record<string, any>;
}
export declare function FlowProvider({ config: initialConfig, queryClient: _queryClient, flowJson, children, }: PropsWithChildren<FlowProviderProps>): React.JSX.Element;
export {};
//# sourceMappingURL=FlowProvider.d.ts.map