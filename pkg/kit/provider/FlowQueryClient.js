import React, { createContext, useContext } from "react";
const FlowQueryClientContext = createContext(undefined);
export function FlowQueryClientProvider({ queryClient, children, }) {
    return (<FlowQueryClientContext.Provider value={queryClient} children={children}/>);
}
export function useFlowQueryClient() {
    const queryClient = useContext(FlowQueryClientContext);
    if (!queryClient) {
        throw new Error("useFlowQueryClient must be used within a FlowQueryClientProvider");
    }
    return queryClient;
}
