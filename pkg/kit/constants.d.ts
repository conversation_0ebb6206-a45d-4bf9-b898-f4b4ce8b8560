export declare const CONTRACT_ADDRESSES: {
    testnet: {
        EVM: string;
        FungibleToken: string;
        NonFungibleToken: string;
        ViewResolver: string;
        MetadataViews: string;
        FlowToken: string;
        ScopedFTProviders: string;
        FlowEVMBridge: string;
        FlowEVMBridgeUtils: string;
        FlowEVMBridgeConfig: string;
        FungibleTokenMetadataViews: string;
    };
    mainnet: {
        EVM: string;
        FungibleToken: string;
        NonFungibleToken: string;
        ViewResolver: string;
        MetadataViews: string;
        FlowToken: string;
        ScopedFTProviders: string;
        FlowEVMBridge: string;
        FlowEVMBridgeUtils: string;
        FlowEVMBridgeConfig: string;
        FungibleTokenMetadataViews: string;
    };
};
export declare const CADENCE_UFIX64_PRECISION = 8;
export declare const DEFAULT_EVM_GAS_LIMIT = "15000000";
//# sourceMappingURL=constants.d.ts.map