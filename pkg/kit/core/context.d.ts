import { FlowNetwork } from "./types";
export type FlowConfig = {
    accessNodeUrl?: string;
    appDetailTitle?: string;
    appDetailIcon?: string;
    appDetailDescription?: string;
    appDetailUrl?: string;
    discoveryWallet?: string;
    discoveryWalletMethod?: string;
    fclLimit?: number;
    flowNetwork?: FlowNetwork;
    serviceOpenIdScopes?: string[];
    walletconnectProjectId?: string;
    walletconnectDisableNotifications?: boolean;
};
export declare const FlowConfigContext: import("react").Context<FlowConfig>;
//# sourceMappingURL=context.d.ts.map