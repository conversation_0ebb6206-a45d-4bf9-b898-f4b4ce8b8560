import { UseMutateAsyncFunction, UseMutateFunction, UseMutationOptions, UseMutationResult } from "@tanstack/react-query";
import { EvmBatchCall } from "./useCrossVmBatchTransaction";
export interface UseCrossVmSpendNftTxArgs {
    mutation?: Omit<UseMutationOptions<string, Error, UseCrossVmSpendNftTxMutateArgs>, "mutationFn">;
}
export interface UseCrossVmSpendNftTxMutateArgs {
    nftIdentifier: string;
    nftIds: string[];
    calls: EvmBatchCall[];
}
export interface UseCrossVmSpendNftTxResult extends Omit<UseMutationResult<string, Error>, "mutate" | "mutateAsync"> {
    spendNft: UseMutateFunction<string, Error, UseCrossVmSpendNftTxMutateArgs>;
    spendNftAsync: UseMutateAsyncFunction<string, Error, UseCrossVmSpendNftTxMutateArgs>;
}
export declare const getCrossVmSpendNftransaction: (chainId: string) => string;
/**
 * Hook to send a cross-VM NFT spend transaction. This function will
 * bundle multiple EVM calls into one atomic Cadence transaction and return the transaction ID.
 *
 * Use `useCrossVmSpendNftStatus` to watch the status of the transaction and get the transaction id + result of each EVM call.
 *
 * @returns The mutation object used to send the transaction.
 */
export declare function useCrossVmSpendNft({ mutation: mutationOptions, }?: UseCrossVmSpendNftTxArgs): UseCrossVmSpendNftTxResult;
//# sourceMappingURL=useCrossVmSpendNft.d.ts.map