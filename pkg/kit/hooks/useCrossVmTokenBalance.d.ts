import { UseQueryOptions } from "@tanstack/react-query";
interface UseCrossVmTokenBalanceArgs {
    owner?: string;
    erc20Address?: string;
    vaultIdentifier?: string;
    query?: Omit<UseQueryOptions<unknown, Error>, "queryKey" | "queryFn">;
}
interface TokenBalance {
    value: bigint;
    formatted: string;
    precision: number;
}
interface UseCrossVmTokenBalanceData {
    cadence: TokenBalance;
    evm: TokenBalance;
    combined: TokenBalance;
}
/**
 * Returns the balance of the owner of a given Fungible Token across both Cadence and EVM accounts.
 * @param param0
 * @returns
 */
export declare function useCrossVmTokenBalance(params: UseCrossVmTokenBalanceArgs): import("@tanstack/react-query").QueryObserverRefetchErrorResult<string, Error> | import("@tanstack/react-query").QueryObserverLoadingErrorResult<string, Error> | import("@tanstack/react-query").QueryObserverRefetchErrorResult<UseCrossVmTokenBalanceData, Error> | import("@tanstack/react-query").QueryObserverSuccessResult<UseCrossVmTokenBalanceData, Error> | import("@tanstack/react-query").QueryObserverLoadingErrorResult<UseCrossVmTokenBalanceData, Error> | import("@tanstack/react-query").QueryObserverPendingResult<UseCrossVmTokenBalanceData, Error> | import("@tanstack/react-query").QueryObserverPlaceholderResult<UseCrossVmTokenBalanceData, Error>;
export {};
//# sourceMappingURL=useCrossVmTokenBalance.d.ts.map