import * as fcl from "@onflow/fcl";
import { useMutation, } from "@tanstack/react-query";
import { useCallback } from "react";
import { useFlowQueryClient } from "../provider/FlowQueryClient";
/**
 * useFlowMutate
 *
 * Sends a Flow transaction via FCL and returns a React Query mutation.
 *
 * @param args.mutation – Optional React Query mutation options
 */
export function useFlowMutate({ mutation: mutationOptions = {}, } = {}) {
    const queryClient = useFlowQueryClient();
    const mutationFn = useCallback(async (variables) => {
        const txId = await fcl.mutate(variables);
        return txId;
    }, []);
    return useMutation({
        mutationFn,
        retry: false,
        ...mutationOptions,
    }, queryClient);
}
