import * as fcl from "@onflow/fcl";
import { useQuery } from "@tanstack/react-query";
import { useFlowQueryClient } from "../provider/FlowQueryClient";
import { useCallback, useMemo } from "react";
/**
 * Fetches a Flow block according to the given params.
 *
 * @param params
 *   - sealed: boolean (optional) – latest sealed block
 *   - id: string (optional)     – block by ID
 *   - height: number (optional) – block by height
 *   - query: (optional)         – React Query flags (enabled, staleTime, retry, etc.)
 */
export function useFlowBlock(params = {}) {
    const { sealed, id, height, query: queryOptions = {} } = params;
    const queryClient = useFlowQueryClient();
    const domainParams = useMemo(() => ({ sealed, id, height }), [sealed, id, height]);
    const fetchBlock = useCallback(async () => {
        return (await fcl.block(domainParams));
    }, [domainParams]);
    return useQuery({
        queryKey: ["flowBlock", domainParams],
        queryFn: fetchBlock,
        initialData: null,
        ...queryOptions,
    }, queryClient);
}
