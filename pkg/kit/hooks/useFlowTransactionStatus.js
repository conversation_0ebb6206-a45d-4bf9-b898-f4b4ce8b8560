import { useState, useEffect } from "react";
import * as fcl from "@onflow/fcl";
/**
 * Subscribes to status updates for a given Flow transaction ID.
 *
 * @remarks
 * This hook was previously named `useFlowTransaction`.
 *
 * @param args.id - The Flow transaction ID to watch
 * @returns {UseFlowTransactionStatusResult}
 */
export function useFlowTransactionStatus({ id, }) {
    const [transactionStatus, setTransactionStatus] = useState(null);
    const [error, setError] = useState(null);
    useEffect(() => {
        if (!id)
            return;
        setTransactionStatus(null);
        setError(null);
        const tx = fcl.tx(id);
        const unsubscribe = tx.subscribe((updatedStatus) => {
            setTransactionStatus(updatedStatus);
            if (updatedStatus.errorMessage) {
                setError(fcl.TransactionError.fromErrorMessage(updatedStatus.errorMessage));
            }
        });
        return () => {
            unsubscribe();
        };
    }, [id]);
    return { transactionStatus, error };
}
