import * as fcl from "@onflow/fcl";
import { UseMutationResult, UseMutationOptions } from "@tanstack/react-query";
/**
 * Arguments for the useFlowMutate hook.
 *
 * - `mutation`: Optional React Query mutation settings
 *   (e.g. `onSuccess`, `onError`, `retry`, `retryDelay`, etc.).
 */
export interface UseFlowMutateArgs {
    mutation?: Omit<UseMutationOptions<string, Error, Parameters<typeof fcl.mutate>[0]>, "mutationFn">;
}
/**
 * useFlowMutate
 *
 * Sends a Flow transaction via FCL and returns a React Query mutation.
 *
 * @param args.mutation – Optional React Query mutation options
 */
export declare function useFlowMutate({ mutation: mutationOptions, }?: UseFlowMutateArgs): UseMutationResult<string, Error, Parameters<typeof fcl.mutate>[0]>;
//# sourceMappingURL=useFlowMutate.d.ts.map