import * as fcl from "@onflow/fcl";
import { UseQueryResult, UseQueryOptions } from "@tanstack/react-query";
export interface UseFlowQueryRawArgs {
    cadence: string;
    args?: (arg: typeof fcl.arg, t: typeof fcl.t) => unknown[];
    query?: Omit<UseQueryOptions<unknown, Error>, "queryKey" | "queryFn">;
}
/**
 * useFlowQueryRaw
 *
 * Executes a Cadence script and returns the raw query result.
 *
 * @param params
 *   - cadence: The Cadence script to run
 *   - args: (optional) A function returning script arguments
 *   - query: (optional) React Query settings (staleTime, retry, enabled, select, etc.)
 * @returns {UseQueryResult<unknown, Error>}
 */
export declare function useFlowQueryRaw({ cadence, args, query: queryOptions, }: UseFlowQueryRawArgs): UseQueryResult<unknown, Error>;
//# sourceMappingURL=useFlowQueryRaw.d.ts.map