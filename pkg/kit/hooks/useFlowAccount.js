import * as fcl from "@onflow/fcl";
import { useQuery } from "@tanstack/react-query";
import { useCallback } from "react";
import { useFlowQueryClient } from "../provider/FlowQueryClient";
/**
 * Fetches Flow account data for a given address.
 *
 * @param args.address – Flow address
 * @param args.query – Optional React Query options
 */
export function useFlowAccount({ address, query: queryOptions = {}, }) {
    const queryClient = useFlowQueryClient();
    const fetchAccount = useCallback(async () => {
        if (!address)
            return null;
        return (await fcl.account(address));
    }, [address]);
    return useQuery({
        queryKey: ["flowAccount", address],
        queryFn: fetchAccount,
        initialData: null,
        ...queryOptions,
    }, queryClient);
}
