import { Abi } from "viem";
import { UseMutateAsyncFunction, UseMutateFunction, UseMutationOptions, UseMutationResult } from "@tanstack/react-query";
interface UseCrossVmBatchTransactionMutateArgs {
    calls: EvmBatchCall[];
    mustPass?: boolean;
}
export interface UseCrossVmBatchTransactionArgs {
    mutation?: Omit<UseMutationOptions<string, Error, UseCrossVmBatchTransactionMutateArgs>, "mutationFn">;
}
export interface UseCrossVmBatchTransactionResult extends Omit<UseMutationResult<string, Error, UseCrossVmBatchTransactionMutateArgs>, "mutate" | "mutateAsync"> {
    sendBatchTransaction: UseMutateFunction<string, Error, UseCrossVmBatchTransactionMutateArgs>;
    sendBatchTransactionAsync: UseMutateAsyncFunction<string, Error, UseCrossVmBatchTransactionMutateArgs>;
}
export interface EvmBatchCall {
    address: string;
    abi: Abi;
    functionName: string;
    args?: readonly unknown[];
    gasLimit?: bigint;
    value?: bigint;
}
export declare function encodeCalls(calls: EvmBatchCall[]): Array<{
    to: string;
    data: string;
    gasLimit: string;
    value: string;
}>;
export declare const getCadenceBatchTransaction: (chainId: string) => string;
/**
 * Hook to send an EVM batch transaction using a Cadence-compatible wallet.  This function will
 * bundle multiple EVM calls into one atomic Cadence transaction and return both the Cadence
 * transaction ID as well as the result of each EVM call.
 *
 * @returns The query mutation object used to send the transaction and get the result.
 */
export declare function useCrossVmBatchTransaction({ mutation: mutationOptions, }?: UseCrossVmBatchTransactionArgs): UseCrossVmBatchTransactionResult;
export {};
//# sourceMappingURL=useCrossVmBatchTransaction.d.ts.map