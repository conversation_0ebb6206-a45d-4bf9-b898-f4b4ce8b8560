import { Event } from "@onflow/typedefs";
export interface EventFilter {
    startBlockId?: string;
    startHeight?: number;
    eventTypes?: string[];
    addresses?: string[];
    contracts?: string[];
    opts?: {
        heartbeatInterval?: number;
    };
}
export interface UseFlowEventsArgs extends EventFilter {
    /** Called for each new event received */
    onEvent: (event: Event) => void;
    /** Optional error callback */
    onError?: (error: Error) => void;
}
/**
 * useFlowEvents hook
 *
 * Subscribes to a Flow event stream and calls the provided callbacks.
 */
export declare function useFlowEvents({ startBlockId, startHeight, eventTypes, addresses, contracts, opts, onEvent, onError, }: UseFlowEventsArgs): void;
//# sourceMappingURL=useFlowEvents.d.ts.map