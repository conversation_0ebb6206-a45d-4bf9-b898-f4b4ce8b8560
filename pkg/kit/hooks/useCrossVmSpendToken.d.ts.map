{"version": 3, "file": "useCrossVmSpendToken.d.ts", "sourceRoot": "", "sources": ["../../../packages/kit/src/hooks/useCrossVmSpendToken.ts"], "names": [], "mappings": "AACA,OAAO,EACL,sBAAsB,EACtB,iBAAiB,EAEjB,kBAAkB,EAClB,iBAAiB,EAClB,MAAM,uBAAuB,CAAA;AAG9B,OAAO,EAAc,YAAY,EAAC,MAAM,8BAA8B,CAAA;AAGtE,MAAM,WAAW,wBAAwB;IACvC,QAAQ,CAAC,EAAE,IAAI,CACb,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,8BAA8B,CAAC,EACjE,YAAY,CACb,CAAA;CACF;AAED,MAAM,WAAW,8BAA8B;IAC7C,eAAe,EAAE,MAAM,CAAA;IACvB,MAAM,EAAE,MAAM,CAAA;IACd,KAAK,EAAE,YAAY,EAAE,CAAA;CACtB;AAED,MAAM,WAAW,0BACf,SAAQ,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,aAAa,CAAC;IACxE,UAAU,EAAE,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,8BAA8B,CAAC,CAAA;IAC5E,eAAe,EAAE,sBAAsB,CACrC,MAAM,EACN,KAAK,EACL,8BAA8B,CAC/B,CAAA;CACF;AAGD,eAAO,MAAM,+BAA+B,GAAI,SAAS,MAAM,WAqK9D,CAAA;AAED;;;;;GAKG;AACH,wBAAgB,oBAAoB,CAAC,EACnC,QAAQ,EAAE,eAAoB,GAC/B,GAAE,wBAA6B,GAAG,0BAA0B,CA2D5D"}