import * as fcl from "@onflow/fcl";
import { useQuery } from "@tanstack/react-query";
import { useFlowQueryClient } from "../provider/FlowQueryClient";
import { useCallback } from "react";
import { useFlowConfig } from "./useFlowConfig";
/**
 * Gets the Flow chain ID.
 */
export function useFlowChainId({ query: queryOptions = {}, } = {}) {
    const queryClient = useFlowQueryClient();
    const config = useFlowConfig();
    const fetchChainId = useCallback(async () => {
        return await fcl.getChainId();
    }, [config]);
    return useQuery({
        queryKey: ["flowChainId"],
        queryFn: fetchChainId,
        initialData: null,
        ...queryOptions,
    }, queryClient);
}
