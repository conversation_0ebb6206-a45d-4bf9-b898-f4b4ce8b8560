import * as fcl from "@onflow/fcl";
import { useQuery } from "@tanstack/react-query";
import { useCallback } from "react";
import { useFlowQueryClient } from "../provider/FlowQueryClient";
export function encodeQueryArgs(args) {
    // Encode the arguments to a JSON-CDC object so they can be deterministically
    // serialized and used as the query key.
    return args?.(fcl.arg, fcl.t)?.map((x) => x.xform.asArgument(x.value));
}
/**
 * useFlowQuery
 *
 * Executes a Cadence script and returns the query result.
 *
 * @param params
 *   - cadence: The Cadence script to run
 *   - args: (optional) A function returning script arguments
 *   - query: (optional) React Query settings (staleTime, retry, enabled, select, etc.)
 * @returns {UseQueryResult<unknown, Error>}
 */
export function useFlowQuery({ cadence, args, query: queryOptions = {}, }) {
    const queryClient = useFlowQueryClient();
    const fetchQuery = useCallback(async () => {
        if (!cadence)
            return null;
        return fcl.query({ cadence, args });
    }, [cadence, args]);
    const encodedArgs = encodeQueryArgs(args);
    return useQuery({
        queryKey: ["flowQuery", cadence, encodedArgs],
        queryFn: fetchQuery,
        enabled: queryOptions.enabled ?? true,
        ...queryOptions,
    }, queryClient);
}
