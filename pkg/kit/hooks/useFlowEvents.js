import * as fcl from "@onflow/fcl";
import { useEffect } from "react";
/**
 * useFlowEvents hook
 *
 * Subscribes to a Flow event stream and calls the provided callbacks.
 */
export function useFlowEvents({ startBlockId, startHeight, eventTypes, addresses, contracts, opts, onEvent, onError, }) {
    useEffect(() => {
        let unsubscribe;
        try {
            const filter = {
                startBlockId,
                startHeight,
                eventTypes,
                addresses,
                contracts,
                opts,
            };
            unsubscribe = fcl.events(filter).subscribe((newEvent) => {
                if (newEvent) {
                    onEvent(newEvent);
                }
            });
        }
        catch (err) {
            const error = err instanceof Error ? err : new Error(String(err));
            onError?.(error);
        }
        return () => {
            unsubscribe?.();
        };
    }, [
        startBlockId,
        startHeight,
        JSON.stringify(eventTypes),
        JSON.stringify(addresses),
        JSON.stringify(contracts),
        JSON.stringify(opts),
        onEvent,
        onError,
    ]);
}
