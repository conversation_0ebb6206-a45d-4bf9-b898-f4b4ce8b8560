import { TransactionStatus } from "@onflow/typedefs";
export interface UseCrossVmTransactionStatusArgs {
    /** The Flow transaction ID to monitor */
    id?: string;
}
export interface UseCrossVmTransactionStatusResult {
    /** Latest transaction status, or null before any update */
    transactionStatus: TransactionStatus | null;
    /** EVM transaction results, if available */
    evmResults?: CallOutcome[];
    /** Any error encountered during status updates */
    error: Error | null;
}
export interface CallOutcome {
    status: "passed" | "failed" | "skipped";
    hash?: string;
    errorMessage?: string;
}
export interface EvmTransactionExecutedData {
    hash: string[];
    index: string;
    type: string;
    payload: string[];
    errorCode: string;
    errorMessage: string;
    gasConsumed: string;
    contractAddress: string;
    logs: string[];
    blockHeight: string;
    returnedData: string[];
    precompiledCalls: string[];
    stateUpdateChecksum: string;
}
/**
 * Subscribes to status updates for a given Cross-VM Flow transaction ID that executes EVM calls.
 * This hook monitors the transaction status and extracts EVM call results if available.
 *
 * @returns {UseCrossVmTransactionStatusResult}
 */
export declare function useCrossVmTransactionStatus({ id, }: UseCrossVmTransactionStatusArgs): UseCrossVmTransactionStatusResult;
//# sourceMappingURL=useCrossVmTransactionStatus.d.ts.map