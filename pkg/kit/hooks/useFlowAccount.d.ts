import type { Account } from "@onflow/typedefs";
import { UseQueryResult, UseQueryOptions } from "@tanstack/react-query";
export interface UseFlowAccountArgs {
    /** Flow address (with or without `0x`) */
    address?: string;
    /** React Query settings (staleTime, retry, enabled, select, etc.) */
    query?: Omit<UseQueryOptions<Account | null, Error>, "queryKey" | "queryFn">;
}
/**
 * Fetches Flow account data for a given address.
 *
 * @param args.address – Flow address
 * @param args.query – Optional React Query options
 */
export declare function useFlowAccount({ address, query: queryOptions, }: UseFlowAccountArgs): UseQueryResult<Account | null, Error>;
//# sourceMappingURL=useFlowAccount.d.ts.map