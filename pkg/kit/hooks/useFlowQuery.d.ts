import * as fcl from "@onflow/fcl";
import { UseQueryResult, UseQueryOptions } from "@tanstack/react-query";
export declare function encodeQueryArgs(args?: (arg: typeof fcl.arg, t: typeof fcl.t) => unknown[]): any[] | undefined;
export interface UseFlowQueryArgs {
    cadence: string;
    args?: (arg: typeof fcl.arg, t: typeof fcl.t) => unknown[];
    query?: Omit<UseQueryOptions<unknown, Error>, "queryKey" | "queryFn">;
}
/**
 * useFlowQuery
 *
 * Executes a Cadence script and returns the query result.
 *
 * @param params
 *   - cadence: The Cadence script to run
 *   - args: (optional) A function returning script arguments
 *   - query: (optional) React Query settings (staleTime, retry, enabled, select, etc.)
 * @returns {UseQueryResult<unknown, Error>}
 */
export declare function useFlowQuery({ cadence, args, query: queryOptions, }: UseFlowQueryArgs): UseQueryResult<unknown, Error>;
//# sourceMappingURL=useFlowQuery.d.ts.map