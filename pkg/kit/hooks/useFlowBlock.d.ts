import { Block } from "@onflow/typedefs";
import { UseQueryResult, UseQueryOptions } from "@tanstack/react-query";
export interface UseFlowBlockArgs {
    sealed?: boolean;
    id?: string;
    height?: number;
    query?: Omit<UseQueryOptions<Block | null, Error>, "queryKey" | "queryFn">;
}
/**
 * Fetches a Flow block according to the given params.
 *
 * @param params
 *   - sealed: boolean (optional) – latest sealed block
 *   - id: string (optional)     – block by ID
 *   - height: number (optional) – block by height
 *   - query: (optional)         – React Query flags (enabled, staleTime, retry, etc.)
 */
export declare function useFlowBlock(params?: UseFlowBlockArgs): UseQueryResult<Block | null, Error>;
//# sourceMappingURL=useFlowBlock.d.ts.map