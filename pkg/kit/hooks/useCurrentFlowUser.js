import { useState, useEffect } from "react";
import * as fcl from "@onflow/fcl";
export function useCurrentFlowUser() {
    const [user, setUser] = useState(null);
    useEffect(() => {
        const unsubscribe = fcl.currentUser.subscribe(setUser);
        return () => unsubscribe();
    }, []);
    const authenticate = async () => {
        try {
            return await fcl.authenticate();
        }
        catch (error) {
            throw error;
        }
    };
    const unauthenticate = () => {
        fcl.unauthenticate();
    };
    return {
        user,
        authenticate,
        unauthenticate,
    };
}
