import { TransactionStatus } from "@onflow/typedefs";
export interface UseFlowTransactionStatusArgs {
    /** The Flow transaction ID to monitor */
    id?: string;
}
export interface UseFlowTransactionStatusResult {
    /** Latest transaction status, or null before any update */
    transactionStatus: TransactionStatus | null;
    /** Any error encountered during status updates */
    error: Error | null;
}
/**
 * Subscribes to status updates for a given Flow transaction ID.
 *
 * @remarks
 * This hook was previously named `useFlowTransaction`.
 *
 * @param args.id - The Flow transaction ID to watch
 * @returns {UseFlowTransactionStatusResult}
 */
export declare function useFlowTransactionStatus({ id, }: UseFlowTransactionStatusArgs): UseFlowTransactionStatusResult;
//# sourceMappingURL=useFlowTransactionStatus.d.ts.map