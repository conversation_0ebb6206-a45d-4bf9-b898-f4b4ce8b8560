import * as fcl from "@onflow/fcl";
import { CONTRACT_ADDRESSES } from "../constants";
import { useFlowChainId } from "./useFlowChainId";
import { useFlowTransactionStatus } from "./useFlowTransactionStatus";
/**
 * Subscribes to status updates for a given Cross-VM Flow transaction ID that executes EVM calls.
 * This hook monitors the transaction status and extracts EVM call results if available.
 *
 * @returns {UseCrossVmTransactionStatusResult}
 */
export function useCrossVmTransactionStatus({ id, }) {
    const chainId = useFlowChainId();
    const eventType = chainId.data && chainId.data in CONTRACT_ADDRESSES
        ? `A.${fcl.sansPrefix(CONTRACT_ADDRESSES[chainId.data].EVM)}.EVM.TransactionExecuted`
        : null;
    const { transactionStatus, error } = useFlowTransactionStatus({
        id: eventType ? id : undefined,
    });
    if (eventType === null) {
        return {
            transactionStatus: null,
            error: new Error(`Unsupported chain: ${chainId.data}. Please ensure the chain ID is valid and supported.`),
        };
    }
    const evmEvents = transactionStatus?.events
        ?.filter(event => event.type === eventType)
        ?.map(event => event.data);
    const evmResults = evmEvents?.map(event => {
        const { hash, errorCode, errorMessage } = event;
        const result = {
            status: errorCode === "0" ? "passed" : "failed",
            hash: `0x${hash.map(h => parseInt(h, 10).toString(16).padStart(2, "0")).join("")}`,
        };
        if (event.errorMessage) {
            result.errorMessage = errorMessage;
        }
        return result;
    });
    return { transactionStatus, error, evmResults: evmResults };
}
