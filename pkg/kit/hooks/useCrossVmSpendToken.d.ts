import { UseMutateAsyncFunction, UseMutateFunction, UseMutationOptions, UseMutationResult } from "@tanstack/react-query";
import { EvmBatchCall } from "./useCrossVmBatchTransaction";
export interface UseCrossVmSpendTokenArgs {
    mutation?: Omit<UseMutationOptions<string, Error, UseCrossVmSpendTokenMutateArgs>, "mutationFn">;
}
export interface UseCrossVmSpendTokenMutateArgs {
    vaultIdentifier: string;
    amount: string;
    calls: EvmBatchCall[];
}
export interface UseCrossVmSpendTokenResult extends Omit<UseMutationResult<string, Error>, "mutate" | "mutateAsync"> {
    spendToken: UseMutateFunction<string, Error, UseCrossVmSpendTokenMutateArgs>;
    spendTokenAsync: UseMutateAsyncFunction<string, Error, UseCrossVmSpendTokenMutateArgs>;
}
export declare const getCrossVmSpendTokenTransaction: (chainId: string) => string;
/**
 * Hook to send a cross-VM FT spend transaction. This function will
 * bundle multiple EVM calls into one atomic Cadence transaction and return the transaction ID.
 *
 * @returns The mutation object used to send the transaction.
 */
export declare function useCrossVmSpendToken({ mutation: mutationOptions, }?: UseCrossVmSpendTokenArgs): UseCrossVmSpendTokenResult;
//# sourceMappingURL=useCrossVmSpendToken.d.ts.map